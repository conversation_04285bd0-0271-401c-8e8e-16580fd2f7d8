<template>
    <div class="hubble-workspace" v-loading="loading">
        <div class="hubble-workspace__header">
            <div class="hubble-workspace__header-left">
                <img src="./img/logo.png" alt="">
                <div class="hubble-workspace__header-left-title">{{ $t('workspaceIndex.title') }}</div>
            </div>
            <div class="hubble-workspace__header-right">
                <!-- <div class="hubble-workspace__header-package" @click="overviewDialog = true">
                    <div class="hubble-workspace__header-left-title">{{ $t('workspaceIndex.package') }}</div>
                </div> -->
                <LangSwitch cacheOnly></LangSwitch>
                <i class="el-icon-ssq-logout hubble-workspace__header-logout" @click="handleLogout"></i>
            </div>
        </div>
        <div class="hubble-workspace__main">
            <ul class="hubble-workspace__menu">
                <li @click="handleJump(menu.path)" :class="{'hubble-workspace__menu-item': true, active: menu.path === $route.path}" v-for="(menu, i) in menuList" :key="i">
                    <div class="hubble-workspace__menu-name">
                        <i :class="menu.icon"></i>
                        <span>{{ menu.name }}</span>
                    </div>
                    <i class="el-icon-ssq-xiangyou hubble-workspace__menu-icon" v-show="menu.path === $route.path"></i>
                </li>
            </ul>
            <div class="hubble-workspace__content" v-if="workspaceId">
                <router-view></router-view>
            </div>
        </div>
        <OverViewDialog
            :workspaceId="workspaceId"
            :overviewDialog="overviewDialog"
            @closeDialog="overviewDialog = false"
        ></OverViewDialog>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import OverViewDialog from './components/overViewDialog';
import LangSwitch from 'components/langSwitch/index.vue';
export default {
    components: {
        OverViewDialog,
        LangSwitch,
    },
    data() {
        return {
            overviewDialog: false,
            loading: false,
        };
    },
    computed: {
        ...mapState(['workspaceId']),
        menuList() {
            return [{
                name: this.$t('workspaceIndex.agreement'),
                icon: 'el-icon-ssq-xieyi',
                path: '/hubble-workspace/agreement',
            }, {
                name: this.$t('workspaceIndex.review'),
                icon: 'el-icon-ssq-shencha',
                path: '/hubble-workspace/review',
            }, {
                name: this.$t('workspaceIndex.term'),
                icon: 'el-icon-ssq-shuyu',
                path: '/hubble-workspace/term',
            }];
        }
    },
    methods: {
        handleJump(path) {
            this.$router.push({ path: path, query: { ...this.$route.query } });
        },
        getWorkspaceId() {
            this.$http('/web-api/workspace/query').then(res => {
                this.$store.state.workspaceId = res.data.details[0].workspaceId;
                console.log(this.workspaceId);
            });
        },
        handleLogout() {
            this.$cookie.delete('access_token');
            this.$cookie.delete('refresh_token');
            this.loading = true;
            this.$auth0.logout({
                logoutParams: {
                    returnTo: 'https://hubbledoc.ai',
                },
            });
        },
    },
    created() {
        this.getWorkspaceId();
    },
};
</script>

<style lang="scss">
.hubble-workspace{
    display: flex;
    flex-direction: column;
    height: 100vh;
    *{
        box-sizing: border-box;
    }
    .ThemeColor{
        cursor: pointer;
        color: $theme-color;
    }
    &__header{
        height: 54px;
        background: #F8F8F8;
        padding: 15px 30px;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &-left {
            display: flex;
            img{
                height: 24px;
            }
            &-title{
                font-size: 14px;
                padding: 0 5px;
                line-height: 24px;
                background: $theme-color;
                color: #fff;
                border-radius: 5px;
                margin-left: 10px;
            }
        }
        &-right{
            display: flex;
            align-items: center;
        }
        &-package {
            cursor: pointer;
        }
        &-logout {
            margin-left: 20px;
            color: $theme-color;
            cursor: pointer;
        }
    }
    &__main{
        flex-grow: 1;
        display: flex;
    }
    &__menu{
        width: 250px;
        flex-shrink: 0;
        border-right: 1px solid #eee;
        padding: 25px 20px 0 30px;
        &-item{
            line-height: 48px;
            border-radius: 30px;
            margin-bottom: 10px;
            padding: 0 24px;
            display: flex;
            justify-content: space-between;
            cursor: pointer;
            &.active, &:hover{
                background: #F5F4F3;
            }
        }
        &-name{
            i{
                margin-right: 16px;
            }
        }
        &-icon{
            line-height: 48px;
        }
    }
    &__content{
        flex-grow: 1;
    }
    &__title{
        font-size: 18px;
        padding: 0 28px;
        line-height: 60px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        &-btn{
            font-size: 14px;
            height: 36px;
            background: #f8f8f8;
            margin-top: 12px;
        }
    }
    &-table {
        width: calc(100vw - 250px);
        padding: 20px 28px;
        background: #f8f8f8;
        flex-grow: 1;
        .cell{
            padding: 5px 18px;
            white-space: pre-wrap;
            .ellipsis-text{
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3; /* 限制为3行 */
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        &-operate{
            // width: 150px;
            display: flex;
            justify-content: space-between;
            color: #ccc;
            span{
                color: $theme-color;
                cursor: pointer;
            }
        }
    }
    &__dialog-body {
        display: flex;
        &-left {
            padding: 10px;
            background-color: #f8f8f8;
            flex-shrink: 0;
            &-tab {
                padding: 10px 20px;
                background-color: #fff;
                display: flex;
                align-items: center;
                font-size: 14px;
                border-radius: 4px;
                img {
                    display: block;
                    height: 14px;
                    margin-right: 10px;
                }
            }
        }
        &-right {
            padding: 10px 20px;
            background-color: #fff;
            flex-grow: 1;
            &-total {
                font-size: 14px;
                margin-bottom: 10px;
            }
            &-detail {
                margin-bottom: 10px;
                background-color: #fff;
                overflow: hidden;
                &-percent {
                    padding: 20px;
                    background-color: #f8f8f8;
                    border-radius: 4px;
                    margin-bottom: 10px;
                    &-name {
                        font-size: 13px;
                        margin-bottom: 10px;
                    }
                    &-desc {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 12px;
                        margin-top: 10px;
                        span {
                            color: #000;
                        }
                        p {
                            color: #999;;
                        }
                    }
                }
                &-expire {
                    text-align: right;
                    font-size: 12px;
                    color: #999;;
                }
            }
        }
    }
}
</style>
