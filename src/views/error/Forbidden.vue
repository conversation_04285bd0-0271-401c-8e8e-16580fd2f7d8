<!-- 403页 -->
<template>
    <Error class="forbidden-page">
        <p class="p1">403</p>
        <p class="p2">{{ $t('tips.notAccessPage') }}</p>
    </Error>
</template>
<script>
import Error from './common/Error.vue';

export default {
    components: {
        Error,
    },
};
</script>
<style lang="scss">
	.forbidden-page {
		p {
			text-align: center;
		}
		.p1 {
			font-size: 80px;
			color: $--color-text-regular;
		}
		.p2 {
			font-size: 14px;
			color: $--color-info;
		}
	}
</style>
