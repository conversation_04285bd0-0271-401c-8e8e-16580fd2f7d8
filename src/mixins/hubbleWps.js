import { mapState } from 'vuex';

export const hubbleWps = {
    computed: {
        ...mapState('hubble', ['riskWpsEditor', 'wpsReady']),
    },
    methods: {
        async rejectAll() {
            await this.riskWpsEditor.ready();
            const revisions = await this.riskWpsEditor.Application.ActiveDocument.Revisions;
            // eslint-disable-next-line new-cap
            await revisions.RejectAll();
            return Promise.resolve();
        },
        async acceptAll() {
            await this.riskWpsEditor.ready();
            const revisions = await this.riskWpsEditor.Application.ActiveDocument.Revisions;
            // eslint-disable-next-line new-cap
            await revisions.AcceptAll();
            return Promise.resolve();
        },
        handleWpsData(totalResponse) {
            const revisionContentsMatch = totalResponse.match(/<revisionContents>([\s\S]*?)<\/revisionContents>/);
            if (revisionContentsMatch && revisionContentsMatch[1].trim()) {
                const revisionContents = revisionContentsMatch[1];
                return this.handleWpsSuggest(revisionContents);
            }
            return { originalContent: '', proposalContent: '' };
        },
        handleWpsSuggest(revisionContents) {
            let originalContent = '';
            let proposalContent = '';
            const originalMatch = revisionContents.match(/<original>([\s\S]*?)<\/original>/);
            const proposalMatch = revisionContents.match(/<proposal>([\s\S]*?)<\/proposal>/);
            if (originalMatch) {
                originalContent = this.formatString(originalMatch[1]); // 获取 <original> 标签内的内容并去除多余空白
            }
            if (proposalMatch) {
                proposalContent = this.formatString(proposalMatch[1]); // 获取 <proposal> 标签内的内容并去除多余空白
            }
            return { originalContent, proposalContent };
        },
        async replaceText() {
            console.log('wpsReady', this.wpsReady);
            this.wpsChangeMsg = '';
            const app = this.riskWpsEditor.Application;
            const { originalContent, proposalContent } = this.riskWpsBuffer;
            if (!originalContent || !proposalContent) {
                return Promise.resolve();
            }
            const originArr = originalContent.split(/[\n\s]+/);
            const first = originArr[0];
            console.log('first', first);
            await this.riskWpsEditor.ready();
            // eslint-disable-next-line new-cap
            const firstResults = await app.ActiveDocument.Find.Execute(first);
            console.log('firstResults', firstResults);
            if (!firstResults[0]) {
                return Promise.resolve();
            }
            const last = originArr[originArr.length - 1];
            console.log('last', last);
            // eslint-disable-next-line new-cap
            const lastResults = await app.ActiveDocument.Find.Execute(last);
            console.log('lastResults', lastResults);
            const lastResult = lastResults.find(el => el.pos > firstResults[0].pos + firstResults[0].len) || firstResults[0];
            console.log('firstResults[0]', firstResults[0].pos);
            const start = firstResults[0].pos;
            const end = lastResult.pos + lastResult.len;
            // eslint-disable-next-line new-cap
            const range = await app.ActiveDocument.Range(start, end);
            // eslint-disable-next-line new-cap
            await app.ActiveDocument.ActiveWindow.ScrollIntoView(range);
            range.Text = proposalContent;
            return Promise.resolve();
        },
        async handleWpsChangeMsg() {
            await this.riskWpsEditor.ready();
            console.log('ready', true);
            const app = this.riskWpsEditor.Application;
            const revisions = await app.ActiveDocument.Revisions;
            // eslint-disable-next-line new-cap
            const revisionData = await revisions.Json();
            console.log('revisionData', revisionData);
            revisions.ShowRevisionsFrame = true;
            if (revisionData.length) {
                const changes = await this.handleWpsChanges(revisionData);
                this.wpsChangeMsg = `\n<submitArgs><tools><editorRevisions>{"executionResult":"success","changes": ${changes}}</editorRevisions></tools></submitArgs>`;
                await this.acceptAll();
            }
            return Promise.resolve();
        },
        async handleWpsChanges(data) {
            // 使用 Promise.all 等待所有 Promise 完成
            const changesArray = await Promise.all(data.map(async el => {
                const { begin, end, content, leader } = el;
                const app = this.riskWpsEditor.Application;
                const newStart = begin - 20 > 0 ? begin - 20 : 1;
                // eslint-disable-next-line new-cap
                const newRange = app.ActiveDocument.Range(newStart, end + 20);
                const newContent = await newRange.Text; // 获取 newContent
                return {
                    newContent: newContent,
                    changeDesc: `${leader} ${content}`,
                };
            }));
            return JSON.stringify(changesArray); // 返回 JSON 字符串
        },
        formatString(str) {
            return str.replace(/^\s+|\s+$/g, '').replace(/\s+/g, ' ');
        },
    },
};
