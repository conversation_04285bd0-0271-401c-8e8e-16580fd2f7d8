<template>
    <div class="deep-think">
        <div class="deep-think__title" @click="showContent = !showContent">
            <i class="el-icon-ssq-shendusikao"></i>
            <span>{{ title }}</span>
            <i class="el-icon-ssq-xialajiantou" :class="{'icon-open': showContent}"></i>
        </div>
        <el-collapse-transition :duration="300">
            <div class="deep-think__content" v-show="showContent">
                <VueMarkdown
                    :source="content"
                    v-if="content"
                ></VueMarkdown>
            </div>
        </el-collapse-transition>
    </div>
</template>

<script>
import VueMarkdown from 'vue-markdown';
export default {
    name: 'DeepThink',
    components: {
        VueMarkdown,
    },
    props: {
        isThinking: {
            type: Boolean,
            default: false,
        },
        content: {
            type: String,
            default: '',
        },
        isOpen: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            showContent: this.isOpen,
        };
    },
    computed: {
        title() {
            return this.isThinking ? this.$t('agent.deepThinking') : this.$t('agent.deepThoughtCompleted');
        },
    },
};
</script>

<style lang="scss">
.deep-think {
    overflow: hidden;
    margin-bottom: 10px;
    &__title {
        width: 120px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        color: #333;
        margin-bottom: 10px;
        cursor: pointer;
        background: #fff;
        border-radius: 5px;
        i {
            font-size: 24px;
            margin: 0 5px;
            &.el-icon-ssq-xialajiantou {
                font-size: 12px;
                &.icon-open {
                    transform: rotate(180deg);
                }
            }
        }
    }
    &__content {
        color: #666;
        font-size: 12px;
        padding-left: 10px;
        border-left: 2px solid #ccc;
    }
}
</style>
