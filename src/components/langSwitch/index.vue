<template>
    <el-popover
        placement="top"
        width="80"
        v-model="visible"
        popper-class="lang-switch-popover"
    >
        <ul class="language-list">
            <li
                v-for="item in languageList"
                :key="item.lang"
                @click="switchLang(item)"
                class="list-item"
            >
                <span class="item-text">{{ item.text }}</span>
                <i v-if="lang === item.lang" class="iconfont el-icon-ssq-xuanzhong"></i>
            </li>
        </ul>
        <span slot="reference" class="lang-switch cur-pointer">
            <i class="el-icon-ssq-diqiu"></i>
            {{ currentLanguage[lang] }}
        </span>
    </el-popover>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    name: 'LangSwitch',
    props: {
        cacheOnly: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            visible: false,
        };
    },
    computed: {
        ...mapGetters(['getIsJa']),
        lang() {
            return this.$i18n.locale;
        },
        currentLanguage() {
            return {
                zh: '语言',
                en: 'Switch language',
                ja: '言語',
                ru: 'Переключение языков',
            };
        },
        languageList() {
            return [
                { lang: 'zh', text: '中文' },
                { lang: 'en', text: 'English' },
                { lang: 'ja', text: '日本語' },
            ];
        },
    },
    methods: {
        switchLang(item) {
            const targetLang = item.lang; // 语言切换
            this.$emit('changeLanguage', item);
            if (this.cacheOnly) { // 仅切换
                this.$i18n.locale = targetLang;
                this.$cookie.set('language', targetLang);
                this.visible = false;
                return;
            }
            this.$http.post('/users/configs/VIEW_LANGUAGE', {
                name: 'VIEW_LANGUAGE',
                value: targetLang,
            }).then(() => {
                // 更新cookie，后端根据cookie做翻译
                this.$cookie.set('language', targetLang);
                this.visible = false;
                location.reload(); // 部分文案在代码逻辑中确定，直接修改locale逻辑不会重新执行，刷新界面
            });
        },
    },
};
</script>

<style lang="scss">
.lang-switch-popover.el-popper{
    padding: 12px 10px;
    width: 80px;
    min-width: inherit;
    background: #303133;
    color: #fff;
    border: 1px solid #303133;
    .language-list{
        .list-item{
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            font-size: 14px;
            font-weight: 300;
            cursor: pointer;
            &:nth-child(2){
                margin: 12px 0;
            }
            &:hover{
                color: $--color-primary;
            }
            .iconfont{
                color: $--color-primary;
            }
        }
    }
}
.lang-switch-popover.el-popper[x-placement^=top] .popper__arrow{
    border-top-color: #303133;
}
.lang-switch-popover.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: #303133;
}
.lang-switch-popover.el-popper[x-placement^=bottom] .popper__arrow{
    border-bottom-color: #303133;
}
.lang-switch-popover.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: #303133;
}
.lang-switch {
    display: inline-block;
    margin-right: 5px;
    margin-left: 5px;
    font-size: 14px;
    color: $theme-color;
    i {
        font-size: 16px;
        padding-right: 2px;
        vertical-align: text-bottom;
    }
}
</style>
