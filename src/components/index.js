import {
    Pagination,
    Dialog,
    // Autocomplete,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    // Menu,
    // Submenu,
    // MenuItem,
    // MenuItemGroup,
    Input,
    // InputNumber,
    Radio,
    RadioGroup,
    // RadioButton,
    Checkbox,
    // CheckboxButton,
    CheckboxGroup,
    // Switch,
    Select,
    Option,
    // OptionGroup,
    Button,
    // ButtonGroup,
    Table,
    TableColumn,
    // DatePicker,
    // TimeSelect,
    // TimePicker,
    Popover,
    Tooltip,
    // Breadcrumb,
    // BreadcrumbItem,
    Form,
    FormItem,
    Tabs,
    TabPane,
    // Tag,
    // Tree,
    // Alert,
    // Slider,
    // Icon,
    Row,
    Col,
    Upload,
    Progress,
    // Spinner,
    // Badge,
    // Card,
    // Rate,
    Steps,
    Step,
    Carousel,
    CarouselItem,
    Collapse,
    CollapseItem,
    // Cascader,
    // ColorPicker,
    // Transfer,
    // Container,
    // Header,
    // Aside,
    // Main,
    // Footer,
    // Timeline,
    // TimelineItem,
    // Link,
    // Divider,
    // Image,
    // Calendar,
    // Backtop,
    // PageHeader,
    // CascaderPanel,
    Loading,
    MessageBox,
    // Message,
    // Notification
} from 'element-ui';
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition';
const components = {
    Pagination,
    Dialog,
    // Autocomplete,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    // Menu,
    // Submenu,
    // MenuItem,
    // MenuItemGroup,
    Input,
    // InputNumber,
    Radio,
    RadioGroup,
    // RadioButton,
    Checkbox,
    // CheckboxButton,
    CheckboxGroup,
    // Switch,
    Select,
    Option,
    // OptionGroup,
    Button,
    // ButtonGroup,
    Table,
    TableColumn,
    // DatePicker,
    // TimeSelect,
    // TimePicker,
    Popover,
    Tooltip,
    // Breadcrumb,
    // BreadcrumbItem,
    Form,
    FormItem,
    Tabs,
    TabPane,
    // Tag,
    // Tree,
    // Alert,
    // Slider,
    // Icon,
    Row,
    Col,
    Upload,
    Progress,
    // Spinner,
    // Badge,
    // Card,
    // Rate,
    Steps,
    Step,
    Carousel,
    CarouselItem,
    Collapse,
    CollapseItem,
    CollapseTransition,
    // Cascader,
    // ColorPicker,
    // Transfer,
    // Container,
    // Header,
    // Aside,
    // Main,
    // Footer,
    // Timeline,
    // TimelineItem,
    // Link,
    // Divider,
    // Image,
    // Calendar,
    // Backtop,
    // PageHeader,
    // CascaderPanel
};

/* 注册全局组件 */
Object.keys(components).forEach((key) => {
    var name = 'el-' + key.replace(/([a-z])([A-Z])/, '$1-$2').toLowerCase();
    Vue.component(name, components[key]);
});

Vue.use(Loading.directive);

Vue.prototype.$loading = Loading.service;
Vue.prototype.$msgbox = MessageBox;
Vue.prototype.$alert = MessageBox.alert;
Vue.prototype.$confirm = MessageBox.confirm;
// Vue.prototype.$prompt = MessageBox.prompt;
// Vue.prototype.$notify = Notification;
// Vue.prototype.$message = Message;
