<template>
    <div class="hubble-workspace__field-config">
        <el-button @click="showDialog = true">{{ $t('fieldConfig.button') }}</el-button>
        <div class="hubble-workspace__field-config-mask" v-show="showDialog" @click="cancel"></div>
        <div class="hubble-workspace__field-config-dialog" v-show="showDialog">
            <div class="hubble-workspace__field-config-dialog__header">{{ $t('fieldConfig.header') }}</div>
            <el-checkbox-group class="hubble-workspace__field-config-dialog__content" v-model="checkList">
                <el-checkbox v-for="field in fieldList" :disabled="field.fieldSource === 'SYSTEM_DEFAULT'" :key="field.termId" :class="{active: checkList.includes(field.termId)}" :label="field.termId">{{ field.fieldName }}</el-checkbox>
            </el-checkbox-group>
            <div class="hubble-workspace__field-config-dialog__footer">
                <el-button type="primary" @click="submit">{{ $t('fieldConfig.submit') }}</el-button>
                <el-button @click="cancel">{{ $t('fieldConfig.cancel') }}</el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        fieldList: {
            type: Array,
            default: () => [],
        },
        workspaceId: {
            type: String,
            default: '',
        },
        viewId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            showDialog: false,
            checkList: [],
        };
    },
    watch: {
        fieldList: {
            handler(val) {
                this.checkList = val.filter(el => el.display).map(el => el.termId);
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        submit() {
            this.fieldList.forEach(el => {
                el.display = this.checkList.includes(el.termId);
            });
            this.$http.post(`/web-api/workspace/${this.workspaceId}/agreements/list-views/save-fields-display`, {
                workspaceId: this.workspaceId,
                viewId: this.viewId,
                fieldsDisplayList: this.fieldList,
            }).then(() => {
                this.$emit('change', this.fieldList);
                this.showDialog = false;
            });
        },
        cancel() {
            this.checkList = this.fieldList.filter(el => el.display).map(el => el.termId);
            this.showDialog = false;
        },
    },
};
</script>

<style lang="scss">
.hubble-workspace__field-config{
    display: inline-block;
    margin-left: 6px;
    position: relative;
    &-mask{
        position: fixed;
        width: 100vw;
        height: 100vh;
        top: 0;
        left: 0;
        z-index: 9;
    }
    &-dialog{
        width: 240px;
        height: 360px;
        position: absolute;
        z-index: 99;
        right: 0;
        top: 53px;
        background: #fff;
        border-radius: 5px;
        box-shadow: 0px 4px 16px 0px rgba(55, 59, 62, 0.1);
        display: flex;
        flex-direction: column;
        &__header{
            line-height: 50px;
            padding: 0 28px;
            background: #f8f8f8;
            border-bottom: 1px solid #eee;
        }
        &__content{
            flex-grow: 1;
            overflow: auto;
            padding: 9px 14px;
            .el-checkbox{
                display: block;
                margin: 5px 0;
                line-height: 36px;
                padding: 0 12px;
                &.active{
                    background: #f8f8f8;
                }
            }
        }
        &__footer{
            padding: 20px 25px;
            text-align: center;
            border-top: 1px solid #eee;
            .el-button{
                padding: 10px 15px !important;
            }
        }
    }
}
</style>
