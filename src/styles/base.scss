@charset "utf-8";

html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body, input, textarea, select, button{
    font-family: -apple-system, BlinkMacSystemFont, Microsoft YaHei, 'Helvetica Neue', sans-serif;
    font-weight: 400;
}

body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td, hr, button, article, aside, details, figcaption, figure, footer, header, menu, nav, section {
    margin: 0;
    padding: 0
}

i{
    font-style: normal;
}

input::-ms-clear, input::-ms-reveal{display:none;}

input, select, textarea {
    font-size: 100%
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

fieldset, img {
    border: 0
}

img {
    display: inline-block;
}

abbr, acronym {
border: 0;
    font-variant: normal
}

del {
    text-decoration: line-through
}

address, caption, cite, code, dfn, em, th, var {
    font-style: normal;
    font-weight: 500
}

ol, ul {
    list-style: none
}

caption, th {
    text-align: left
}

h1, h2, h3, h4, h5, h6 {
    font-size: 100%;
    font-weight: 400
}

q:before, q:after {
    content: ''
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sup {
    top: -.5em
}

sub {
    bottom: -.25em
}

ins, a {
    text-decoration: none
}

/* element icon polyfill */
[class^="el-icon-ssq"], [class*=" el-icon-ssq"] {
    font-family:"iconfont" !important;
    font-size:inherit;
    font-style:normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

[v-cloak] {
    display: none !important;
}
