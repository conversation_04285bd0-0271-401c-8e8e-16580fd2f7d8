<template>
    <div class="hubble-page__login">
        <div class="hubble-page__login-header">
            <img src="~img/bestsign-logo.png" @click="handleLandingPage" alt="" class="hubble-page__login-header__logo">
        </div>
        <div class="hubble-page__login-main">
            <div class="hubble-page__login-main__box">
                <div class="hubble-page__login-banner">
                    <img src="~img/AIBot.png" alt="" class="hubble-logo">
                    <h1>{{ $t('login.welcome') }}</h1>
                    <span>{{ $t('login.welcomeTip') }}</span>
                </div>
                <div class="hubble-page__login-form">
                    <div style="margin-bottom: 40px">{{ $t('login.welcomeTip') }}</div>
                    <el-input v-model="account" :placeholder="$t('tips.inputPhone')">
                        <img src="~img/account.svg" alt="" slot="prepend">
                    </el-input>
                    <AliAutoValidator class="ali-validator"
                        v-if="showAdditionalAfs"
                        @validateSuccess="handleAliValidateSuccess"
                        ref="aliValidator"
                    ></AliAutoValidator>
                    <el-input v-model="verifyCode" :placeholder="$t('tips.inputVerifyCode')" class="verify-code">
                        <img src="~img/verifyCode.svg" alt="" slot="prepend">
                    </el-input>
                    <CountDown
                        class="count-down"
                        ref="btn"
                        :clickedFn="send"
                        :disabled="countDownDisabled"
                        :second="60"
                    ></CountDown>
                    <div class="agree-line">
                        <el-checkbox class="agree-checkbox" v-model="loginAuthAgreement"></el-checkbox>
                        <p class="tip1">
                            {{ $t('login.hasReadAndAgree') }}
                            <em class="common-font-color cur-pointer" @click="showAgreeDialog('service')">《{{ $t('login.bestsignAgreement') }}》</em>
                            、
                            <em class="common-font-color cur-pointer" @click="showAgreeDialog('privacy')">《{{ $t('login.privacyPolicy') }}》</em>
                            {{ $t('login.and') }}
                            <em class="common-font-color cur-pointer" @click="showAgreeDialog('digital')">《{{ $t('login.digitalCertificateAgreement') }}》</em>
                        </p>
                    </div>
                    <el-button :disabled="loading" class="login-btn" type="primary" @click="handleLogin">{{
                        $t('login.name') }}</el-button>
                    <span>{{ $t('login.autoRegisterForNewPhone') }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import resRules from 'utils/regs.js';
import CountDown from 'components/countDown';
import AliAutoValidator from 'components/aliAutoValidator';
export default {
    components: {
        CountDown,
        AliAutoValidator,
    },
    data() {
        return {
            account: '',
            verifyCode: '',
            verifyKey: '',
            showAdditionalAfs: false,
            aliValidateData: {
                sessionId: '',
                sig: '',
                aliToken: '',
            },
            countDownDisabled: false,
            loginAuthAgreement: false,
            agreementDialogVisible: false,
            agreementType: 'service',
            loading: false,
            utmHubble: 'ent',
            cta: '',
        };
    },
    methods: {
        handleLandingPage() {
            const TLD = process.env.NODE_ENV.indexOf('development') > -1 ? 'info' : location.origin.split('.')[2];
            location.href = `https://www.bestsign.${TLD}/hubble`;
        },
        // 发送验证码
        send() {
            if (!this.account.trim()) {
                return  this.$MessageToast.error(this.$t('tips.lackAccount'));
            }

            if (
                !(resRules.userPhone.test(this.account.trim()))
            ) {
                return this.$MessageToast.error('tips.enterCorrectPhoneNum');
            }

            if (this.showAdditionalAfs) {
                if (!this.aliValidateData.sessionId) {
                    this.$MessageToast.error(this.$t('tips.pleaseCheckAuto'));
                    return;
                } else {
                    this.showAdditionalAfs = false;
                }
            }

            this.countDownDisabled = true;
            setTimeout(this.sended, 0);
            // sendVerCode
            this.$http.sendVerCodeNoLogin({
                code: 'B010',
                sendType: 'S',
                target: this.account.replace(/\s+/g, ''),
                ...this.aliValidateData,
                noToast: 1,
            })
                .then(res => {
                    this.$MessageToast.success(this.$t('login.sendSuc'));
                    this.verifyKey = res.data.value;

                    this.aliValidateData = {
                        sessionId: '',
                        sig: '',
                        aliToken: '',
                    };
                })
                .catch(err => {
                    console.log(err);
                    const res = err.response?.data;
                    this.checkGraphCode(res);
                    this.$refs.btn.reset();
                });
        },
        checkGraphCode(res) {
            // 如果错误是图形验证码不能为空，就不显示忘记密码和注册提示
            if (res.message === '验证信息不能为空') {
                this.errorMsg = this.$t('tips.pleaseCheckAuto');
            } else {
                if (this.loginType === 'password') {
                    this.errorMsg = 'unanimous';
                } else {
                    this.errorMsg = '';
                }
            }
            this.showErrorMsg = true;

            if (res.code === '902' || res.code === '100006' || res.code === '100010') {
                if (this.showAdditionalAfs) {
                    this.resetAliValidator();
                } else {
                    this.showAdditionalAfs = true;
                }

                if (res.message !== '验证信息不能为空') {
                    this.$MessageToast.error(res.message);
                } else {
                    this.$MessageToast.error(this.$t('tips.pleaseCheckAuto'));
                }
            } else {
                this.$MessageToast.error(res.message);
            }
        },
        // 验证码已发送
        sended() {
            this.$refs.btn.run();
            this.countDownDisabled = false;
        },
        showAgreeDialog(type) {
            this.agreementDialogVisible = true;
            this.agreementType = type;
        },
        handleAliValidateSuccess({ sessionId, sig, token } = {}) {
            this.aliValidateData.sessionId = sessionId;
            this.aliValidateData.sig = sig;
            this.aliValidateData.aliToken = token;
        },
        handleLocationSearch() {
            const queryString = location.search;
            const params = new URLSearchParams(queryString);
            const { utmHubble, cta } = Object.fromEntries(params.entries());
            utmHubble && (this.utmHubble = utmHubble);
            cta && (this.cta = cta);
        },
        handleLogin() {
            this.loading = true;
            this.$http.post('/auth-center/user/login', {
                account: this.account.replace(/\s+/g, ''),
                verifyCode: this.verifyCode,
                verifyKey: this.verifyKey,
                autoRegister: true,
            }, {
                headers: {
                    'Content-Type': 'application/json; charset=utf-8',
                    'bestsign-registerTrackUrl': `${location.origin}${location.pathname}?utmHubble=${this.utmHubble}&cta=${this.cta}`,
                },
            }).then(({ data }) => {
                const {
                    access_token,
                    refresh_token,
                } = data;
                this.$token.save(access_token, refresh_token);
                location.href = '/hubble/upload';
            }).finally(() => {
                this.loading = false;
            });
        },
    },
    created() {
        this.handleLocationSearch();
    },
};
</script>
<style lang="scss">
.hubble-page__login {
    background: $--background-color-regular;
    height: 100vh;
    display: flex;
    flex-direction: column;
    user-select: none;
    &-header {
        width: 100%;
        height: 60px;
        background: #fff;
        .hubble-page__login-header__logo {
            height: 50px;
            margin: 5px 0 0 120px;
            cursor: pointer;
        }
    }
    &-main {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: auto;
        &__box{
            width: 1020px;
            min-width: 1020px;
            height: 480px;
            background: #fff;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
        }
    }
    &-banner{
        width: 580px;
        height: 480px;
        background: url('~img/loginBg.png');
        background-size: 100%;
        padding: 140px 0 0 80px;
        color: #fff;
        box-sizing: border-box;
        .hubble-logo{
            width: 22px;
            padding: 9px;
            background: #fff;
            border-radius: 6px;
            margin-bottom: 35px;
        }
        h1{
            font-size: 50px;
        }
        span{
            font-size: 20px;
        }
    }
    &-form{
        flex: 1;
        padding: 80px 50px 0;
        .el-input{
            border: 1px solid rgba(231,231,234,1);
            border-radius: 4px;
            margin-bottom: 15px;
            .el-input-group__prepend{
                border: none;
                border-right: 1px solid rgba(231,231,234,1);
                padding: 0 14px;
                background: #fafafb;
            }
            input{
                height: 44px;
                line-height: 44px;
                border: none;
            }
            &.verify-code{
                width: 210px;
            }
        }
        .ali-validator{
            margin-top: 0 !important;
            margin-bottom: 15px;
        }
        .count-down{
            border-color: $--color-primary;
            width: 120px;
            height: 44px;
            background: #fafafb;
            border-radius: 4px;
            float: right;
            &:hover{
                background: #FFF;
            }
        }
        .agree-line{
            display: flex;
            font-size: 14px;
            .agree-checkbox {
                padding-right: 10px;
            }
            em{
                font-weight: normal;
            }
        }
        .login-btn{
            width: 100%;
            padding: 0;
            height: 44px;
            line-height: 44px;
            border-radius: 4px;
            margin: 25px 0 15px;
            &+span{
                font-size: 12px;
                color: #999;
            }
        }
    }
    &-footer {
        background: #fff !important;
    }
}
</style>
