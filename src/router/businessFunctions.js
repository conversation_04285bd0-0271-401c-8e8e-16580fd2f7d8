import Vue from 'vue';
import i18n from 'src/lang';
import VueCookie from 'vue-cookie';
import { isServer } from '../plugins/cookie/index';

/**
 * next为路由导航守卫beforeEach中的next函数
 * SAAS-31983 登录token失效后，重新刷新页面出现白屏现象
 */
export function getHeadInfoData() {
    // 获取commonheader数据
    return Vue.$http.get('/users/head-info')
        .then(async res => {
            if (res && res.data) {
                // 配置用户语言偏好
                if (typeof res.data.VIEW_LANGUAGE === 'string') {
                    i18n.locale = res.data.VIEW_LANGUAGE;
                    VueCookie.set('language', res.data.VIEW_LANGUAGE, { secure: isServer() ? true : null }); // 配置cookie内默认的语言偏好，后端要用
                }
                return Promise.resolve({});
            } else {
                return Promise.reject([{}, {}, {}, {}]);
            }
        });
}

export function handlePwdLoginFlag(to, next) {
    const query = to.query;
    query.isPwdLogin === 'true' && (Vue.localStorage.set('isPwdLogin', true));
    delete query.isPwdLogin;
    next({ path: to.path, query });
}
