import Vuex from 'vuex';
Vue.use(Vuex);
import hubble from './modules/hubble.js';

const state = {
    workspaceId: '',
};

// 提交 mutation，可包含异步操作，通过 store.dispatch 调用
const actions = {
};

// 从 store 中的 state 中派生出一些状态
const getters = {
};

// 更改 store 中的状态，通过 store.commit 调用
const mutations = {
};

export default new Vuex.Store({
    state,
    actions,
    getters,
    mutations,
    modules: {
        hubble,
    },
});
