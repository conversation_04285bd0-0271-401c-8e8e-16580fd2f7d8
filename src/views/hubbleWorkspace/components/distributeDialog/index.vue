<template>
    <el-dialog
        :title="$t('workspace.review.distribution')"
        :visible.sync="distributeDialog"
        width="40%"
        class="hubble-workspace__dialog"
        :before-close="closeDialog"
    >
        <div class="hubble-workspace__dialog-body">
            <el-form style="width: 100%;">
                <el-form-item required :label="$t('workspace.review.users') + $t('workspace.required')">
                    <el-input type="textarea" v-model="users" :rows="4" :placeholder="$t('workspace.review.placeholder')"></el-input>
                </el-form-item>
                <el-form-item :label="$t('workspace.review.message') + $t('workspace.optional')">
                    <el-input type="textarea" v-model="comments" :rows="4"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <el-button class="submit-distribute" :loading="loading" @click="distributeContent" type="primary">{{ $t('workspace.review.submit') }}</el-button>
    </el-dialog>
</template>

<script>
export default {
    props: {
        distributeDialog: {
            type: Boolean,
            default: false,
        },
        noteData: {
            type: Object,
            default: () => {},
        },
        workspaceId: {
            type: String,
            default: '',
        },
        reviewId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            users: '',
            comments: '',
            loading: false,
        };
    },
    watch: {
        distributeDialog(newVal) {
            if (newVal) {
                if (this.noteData.distributionUserAccounts) {
                    this.users = this.noteData.distributionUserAccounts || '';
                }
                this.comments = this.noteData.comment || '';
            }
        },
    },
    methods: {
        closeDialog() {
            // this.users = '';
            this.comments = '';
            this.loading = false;
            this.$emit('closeDialog');
        },
        distributeContent() {
            this.loading = true;
            const { reviewVersionFileId, contentPosition } = this.noteData;
            const data = {
                reviewVersionFileId,
                contentPosition,
                distributionUserAccounts: this.users,
                comment: this.comments,
                distributionIds: this.noteData.distributionIds || [],
            };
            this.$http.post(`/web-api/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/mark-distribute`, data).then(res => {
                this.$emit('saveTempDistribute', res.data);
                this.closeDialog();
            }).finally(() => {
                this.loading = false;
            });
        },
    },
    created() {},
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
    padding: 10px 30px;
    background-color: #f8f8f8;
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    color: #333333;
}
::v-deep .el-dialog__body {
    padding: 14px 30px;
}
::v-deep .el-form-item {
    margin-bottom: 10px;
}
::v-deep .el-textarea .el-textarea__inner {
    border-radius: 6px;
}
.submit-distribute {
    padding: 10px 40px;
    border-radius: 5px;
    margin: auto;
    display: block;
}
</style>
