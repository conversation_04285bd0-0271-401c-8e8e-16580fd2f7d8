/* eslint-disable eqeqeq */
// TODO:  只用在了登陆注册模块、以及涉及到发送短信验证码的部分。其中只有crypto-js在/users/ignore/captcha/notice这个接口中用到，需要做接耦
import axios from 'axios';
axios.getImageVerCode = function(imageKey) {
    return new Promise((resolve) => {
        axios
            .get(`/users/ignore/captcha/base64-image`, {
                params: { imageKey },
            })
            .then(res => {
                resolve(res);
            });
    });
};

/**
 * code:
 * 'B001': 注册
 * 'B002': 忘记密码
 * 'B003': 重置签约密码
 * 'B004': 修改通知方式
 * 'B005': 修改账号
 * 'B006': 更换管理员
 * 'B008': 发送通知到账号
 */

const sendVerCommon = function(opts, type) {
    const {
        code,
        imageCode = '',
        imageKey = '',
        sendType,
        target = '',
        bizTargetKey = '',
        noToast = 0,
        sessionId = '',
        sig = '',
        token = '',
        aliToken = '',
    } = opts;
    let url;
    switch (type) {
        case 1:
            url = '/users/captcha/notice';
            break;
        case 2:
            url = '/users/ignore/captcha/notice';
            break;
        case 3:
            url = '/users/ignore/captcha/notice-token';
            break;
    }

    const params = {
        url: url,
        method: 'post',
        data: {
            code,
            sendType,
            target,
            bizTargetKey,
        },
        noToast: noToast || 0,
    };

    if (type == 2 || type == 1) {
        const AES = require('crypto-js/aes');
        const Utf8 = require('crypto-js/enc-utf8');
        const ECB = require('crypto-js/mode-ecb');
        const Pkcs7 = require('crypto-js/pad-pkcs7');
        // 使用AES对'{code}:{target}'数据进行加密，目的增加中间人修改手机号频繁调接口的难度
        // mod,和padding和后端约定好
        const key = Utf8.parse(Vue.GLOBAL.AES_ENCRYPT_KEY);
        const encryptStr = type == 2 ? `${code}:${target}` : `${code}:${bizTargetKey}`;
        const encryptToken = AES.encrypt(encryptStr, key, {
            mode: ECB,
            padding: Pkcs7,
        }).toString();
        // 可能含有特殊字符，encode下
        const encodeToken = encodeURIComponent(encryptToken);
        params.url = `${url}?encryptToken=${encodeToken}`;
    }

    if (type == 3) {
        Object.assign(params.data, {
            token: token,
        });
    }

    let headersObj;
    if (imageCode !== '' && imageKey !== '') {
        headersObj = {
            'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
                imageCode: imageCode,
                imageKey: imageKey,
            }),
        };
        Object.assign(params, { headers: headersObj });
    }
    if (sessionId !== '') {
        headersObj = {
            'Content-Type': 'application/json; charset=utf-8',
            additionalAfs: JSON.stringify({
                sessionId,
                sig,
                token: aliToken,
            }),
        };
        Object.assign(params, { headers: headersObj });
    }
    return axios(params);
};
// 需要登录
axios.sendVerCode = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 1)
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};
// 不需要登录
axios.sendVerCodeNoLogin = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 2)
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};
// 不需要登录需要token
axios.sendVerCodeNoLoginNeedToken = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 3)
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};
