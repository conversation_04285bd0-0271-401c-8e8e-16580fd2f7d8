<template>
    <div>
        <div class="hubble-component__header">
            <i :class="`slider-switch el-icon-ssq-Hubblewendangliebiao ${ showSlider && 'active' }`" v-if="!$route.meta.isSharePage" @click="toggleSlider(!showSlider)"></i>
            <b>Hubble</b>
            <div class="button-box" v-if="!$route.meta.isSharePage">
                <el-tooltip :open-delay="500" effect="dark" :content="$t('header.accountConfig')">
                    <i class="el-icon-ssq-shezhi2" @click="handleSetting"></i>
                </el-tooltip>
                <!-- <el-tooltip :open-delay="500" effect="dark" :content="$t('header.share')">
                    <i class="el-icon-ssq-Hubblefenxiang" v-if="!!topicId" @click="handleShare"></i>
                </el-tooltip> -->
                <el-tooltip :open-delay="500" effect="dark" :content="$t('header.myVersion')">
                    <i class="el-icon-ssq-Hubblewodebanben" @click="togglePackageDialog(true)"></i>
                </el-tooltip>
                <!-- <el-tooltip :open-delay="500" effect="dark" content="邀请好友">
                    <i class="el-icon-ssq-liwu" @click="inviteDialogVisible = true"></i>
                </el-tooltip> -->
                <el-tooltip :open-delay="500" effect="dark" :content="$t('common.exit')">
                    <i class="el-icon-ssq-tuichu" @click="handleLogout"></i>
                </el-tooltip>
            </div>
            <el-dialog
                class="hubble-component__share-dialog"
                :visible.sync="dialogVisible"
                width="600px"
                :title="$t('header.shareHubble')"
                :close-on-click-modal="false"
            >
                <div class="hubble-component__share-dialog-content">
                    {{ shareData.shareUserName }} {{ $t('header.inviteToHubble.1') }}<br>
                    {{ $t('header.inviteToHubble.2') }} {{ shareData.shareFileName }}<br><br>
                    {{ $t('header.inviteToHubble.3') }}<br>
                    {{ shareData.shareLink }}<br><br>
                    {{ $t('header.inviteToHubble.4') }}<br>
                    {{ shareData.password }} <br><br>
                    {{ $t('header.inviteToHubble.5') }}
                </div>
                <div class="hubble-component__share-dialog-switch">
                    <el-switch :disabled="shareLoading" v-model="shareLinkVisible" @change="handleSwitch"></el-switch>
                    {{ $t('header.openClose') }}
                </div>
                <div class="hubble-component__share-dialog-copy">
                    <el-button
                        type="primary"
                        @click="handleCopy"
                    >{{ $t('common.copyInfo') }}</el-button>
                </div>
            </el-dialog>
            <el-dialog
                class="hubble-component__invite-dialog"
                :visible.sync="inviteDialogVisible"
                width="340px"
                :title="$t('header.invitation')"
                :close-on-click-modal="false"
            >
                <p>{{ $t('header.inviteFriend') }}</p>
                <el-input v-model="inviteAccount" :placeholder="$t('header.inputFriendNumber')"></el-input>
                <el-button
                    type="primary"
                    @click="handleInvite"
                >{{ $t('header.sendInvite') }}</el-button>
            </el-dialog>
            <el-dialog
                class="hubble-component__setting-dialog"
                :visible.sync="settingDialogVisible"
                :title="$t('header.accountConfig')"
                :close-on-click-modal="false"
            >
                <ul class="hubble-component__setting-dialog__tab">
                    <li class="hubble-component__setting-dialog__tab-item">
                        <i class="el-icon-ssq-taocanxiangqing"></i>
                        {{ $t('header.packageDetail') }}
                    </li>
                </ul>
                <div class="hubble-component__setting-dialog__content">
                    <div class="hubble-component__setting-dialog__package">
                        <p>{{ $t('header.packageVersion') }}{{ currentPackageDetail.packageName }}</p>
                        <p>{{ $t('header.currentPackage') }}{{ currentPackageDetail.effectiveTime }} ~ {{ currentPackageDetail.expireTime }}{{ $t('header.validDuration') }}</p>
                        <div class="hubble-component__setting-dialog__dosage">
                            <div>
                                <div class="hubble-component__setting-dialog__dosage-progress">
                                    <div class="hubble-component__setting-dialog__dosage-usage" :style="`width: ${currentPackageDetail.documentPercent}%`"></div>
                                </div>
                                <div class="hubble-component__setting-dialog__dosage-text">
                                    {{ $t('header.hasUsedNum', {num:currentPackageDetail.documentUsage}) }}
                                    <span>{{ $t('header.totalNums', {num:currentPackageDetail.initialDocumentCount}) }}</span>
                                </div>
                            </div>
                            <div>
                                <div class="hubble-component__setting-dialog__dosage-progress">
                                    <div class="hubble-component__setting-dialog__dosage-usage" :style="`width: ${currentPackageDetail.chatPercent}%`"></div>
                                </div>
                                <div class="hubble-component__setting-dialog__dosage-text">
                                    {{ $t('header.hasUsedTimes', {time:currentPackageDetail.chatUsage}) }}
                                    <span>{{ $t('header.totalTimes', {times:currentPackageDetail.initialChatCount})
                                    }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p>{{ $t('header.buyedPackage') }}{{ currentPackage.planOverviewEffectiveTime }} ~ {{
                        currentPackage.planOverviewExpireTime }} {{ $t('header.buyedContent', {num:currentPackage.ownedPlanCount
                    }) }} </p>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
export default {
    data() {
        return {
            dialogVisible: false,
            inviteDialogVisible: false,
            settingDialogVisible: false,
            inviteAccount: '',
            shareData: {},
            showPackageDialog: false,
            shareLoading: false,
            shareLinkVisible: false,
        };
    },
    computed: {
        ...mapState('hubble', ['currentPackage', 'showSlider']),
        topicId() {
            return this.$route.params.topicId || '';
        },
        currentPackageDetail() {
            return this.currentPackage.currentUserPlan;
        },
    },
    methods: {
        ...mapMutations('hubble', ['setPackage', 'togglePackageDialog', 'toggleSlider']),
        async handleSetting() {
            await this.$emit('initPackage');
            this.settingDialogVisible = true;
        },
        async handleLogout() {
            this.$cookie.delete('access_token');
            this.$cookie.delete('refresh_token');
            await this.$auth0.logout();
        },
        handleShare() {
            this.shareLoading = true;
            this.$http.post(`/web-api/topic/${this.topicId}/share`).then((res) => {
                this.shareData = res.data;
                this.shareLinkVisible = res.data.ifOpen;
                this.dialogVisible = true;
            }).finally(() => {
                this.shareLoading = false;
            });
        },
        handleCopy() {
            const textArea = document.createElement('textarea');
            textArea.value =
                `${this.shareData.shareUserName}${this.$t('header.inviteToHubble.1')}\n${this.$t('header.inviteToHubble.2')}${this.shareData.shareFileName}\n\n${this.$t('header.inviteToHubble.3')}\n${this.shareData.shareLink}\n\n${this.$t('header.inviteToHubble.4')}\n${this.shareData.password}\n\n${this.$t('header.inviteToHubble.5')}`;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.$MessageToast({
                message: this.$t('tips.copySuccess'),
                type: 'success',
            });
        },
        handleInvite() {

        },
        handleSwitch(val) {
            this.shareLoading = true;
            this.$http.post(`/web-api/topic/${this.topicId}/share/shutdown?ifShutdown=${!val}`).catch(() => {
                this.shareLinkVisible = !val;
            }).finally(() => {
                this.shareLoading = false;
            });
        },
    },
};
</script>

<style lang="scss">
.hubble-component__header{
    color: #fff;
    height: 60px;
    background: #002B45;
    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.1);
    font-size: 16px;
    line-height: 60px;
    i.slider-switch{
        cursor: pointer;
        transition: color, 0.3s;
        padding: 0 20px;
    }
    b{
        display: inline-block;
        padding: 0 20px;
        border-left: 1px solid #2A4C66;
    }
    .button-box{
        float: right;
        margin: 15px 20px 0 ;
        display: flex;
        i{
            padding: 7px;
            margin-left: 18px;
            border-radius: 4px;
            cursor: pointer;
            &:hover{
                background: #004466;
            }
        }
    }
}

.hubble-component__share-dialog{
    line-height: 22px;
    .el-dialog{
        width: 600px !important;
    }
    &-content{
        color: #333;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #fbfbfb;
    }
    &-switch{
        //position: absolute;
        //bottom: 53px;
        padding-top: 10px;
        display: flex;
        .el-switch{
            transform: scale(.8);
        }
    }
    &-copy {
        text-align: right;
        .el-button{
            padding: 7px 21px;
        }
    }
}
.hubble-component__setting-dialog{
    .el-dialog{
        width: 600px;
        &__body{
            padding: 0;
            display: flex;
        }
    }
    &__tab{
        width: 140px;
        background: #f8f8f8;
        padding: 10px;
        &-item{
            background: #fff;
            line-height: 40px;
            text-align: center;
            i{
                margin-right: 10px;
            }
            &+&-item{
                margin-top: 10px;
            }
        }
    }
    &__content{
        padding: 10px 20px 30px;
        flex: 1;
        border-left: 1px solid #eee;
        p{
            line-height: 35px;
        }
    }
    &__dosage{
        margin: 20px 0 0;
        background: #f8f8f8;
        padding: 25px;
        font-size: 12px;
        line-height: 12px;
        &-progress{
            height: 6px;
            background: #fff;
            border-radius: 4px;
            overflow: hidden;
        }
        &-usage{
            height: 100%;
            width: 0;
            background-image: linear-gradient(90deg, #76C2FF 0%, #0C8AEE 100%);
            border-radius: 4px;
        }
        &-text{
            margin: 12px 0 25px;
            span{
                float: right;
                color: #999;
            }
        }
    }
}
</style>
