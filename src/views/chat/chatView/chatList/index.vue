<template>
    <ul class="hubble-chat__body-box" @scroll="$emit('loadMore', $event)">
        <li class="hubble-chat__body-empty" v-if="!messages.length">
            <img src="~img/empty.png" alt="">
            <p>{{ $t('chat.selectToQuestTip') }}</p>
        </li>
        <li v-for="(topic, index) in currentMessageList" :key="index" class="message">
            <div class="question" v-if="topic.question">
                <img src="~img/avatar.png" class="avatar" alt="">
                <div class="message-content">
                    <div class="quote chat-quote" v-if="topic.quote" @click="$emit('selectQuote', topic.questionDocumentQuote)">
                        {{ topic.quote }}
                    </div>
                    {{ $t('chat.question') }}{{ topic.question }}
                </div>
            </div>
            <span class="time">{{ topic.chatTime }}</span>
            <div class="answer" v-if="topic.answer">
                <img src="~img/AIAvatar.png" class="avatar" alt="">
                <div class="message-content">
                    {{ topic.answer }}
                    <span class="cursor" v-if="showCursor(index)"></span>
                    <!-- <br><span class="explain">{{ $t('chat.explainTip') }}</span> -->
                </div>
            </div>
            <div class="related" v-show="topic.chatId">
                <template v-if="showAnswerDocumentQuotes(topic)">
                    {{ $t('chat.relatedContent') }}
                    <span v-for="(quote, i) in topic.answerDocumentQuotes" @click="$emit('selectQuote', quote)" :key="i">{{ quote.pageNumber }}</span>
                </template>
                <el-tooltip :open-delay="500" effect="dark" :content="$t('chat.deleteConversation')" placement="top">
                    <i class="operate-icon el-icon-ssq-Hubbleshanchu" @click="handleDelete(index)"></i>
                </el-tooltip>
                <el-tooltip :open-delay="500" effect="dark" :content="$t('chat.openContinuousModel')" placement="top">
                    <i class="operate-icon el-icon-ssq-Hubblelianxuduihua" v-if="!isContinuousChat" @click="$emit('showContinuousChat', topic.chatId)"></i>
                </el-tooltip>
            </div>
        </li>
    </ul>
</template>

<script>
export default {
    props: {
        messages: {
            type: Array,
            default: () => [],
        },
        isContinuousChat: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        currentMessageList() {
            return [...this.messages].reverse();
        },
    },
    watch: {
        messages: {
            handler() {
                this.$nextTick(() => {
                    this.scrollList();
                });
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        showAnswerDocumentQuotes(topic) {
            return !!topic.answerDocumentQuotes?.length;
        },
        showCursor(index) {
            return index === 0 && this.typing;
        },
        scrollList() {
            const element = document.querySelector('.continuous-chat')?.querySelector('.hubble-chat__body-box');
            element && (element.scrollTop = element.scrollHeight);
        },
        handleDelete(index) {
            this.$confirm(this.$t('chat.confirmToDelete'), this.$t('common.tip'), {
                confirmButtonText: this.$t('common.confirm'),
                cancelButtonText: this.$t('common.cancel'),
            }).then(() => {
                this.$http.delete(`/web-api/topic/${this.$route.params.topicId}/${this.currentMessageList[index].chatId}`).then(() => {
                    this.$MessageToast(this.$t('tips.deleteSuccess'));
                    this.messages.splice(this.messages.length - index - 1, 1);
                });
            }).catch(() => {});
        },
    },
};
</script>

<style lang="scss">
</style>
