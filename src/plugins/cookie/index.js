import Vue from 'vue';
import VueCookie from 'vue-cookie';
// 锁定浏览器指纹版本以及写法，后续更新需同步所有子项目
import Fingerprint2 from '../../utils/fingerprint2.min.js';
import { isSafari, isChrome, getBroswer } from 'utils/device.js';

// 判断是否是服务端 通过协议为 https/http 来区分
export const isServer = () => {
    return window.location.protocol === 'https:';
};

// 预设 cookie option
function cookieOptionPreset() {
    // production 环境，判断 iframe 下、非 safari 、非chrome版本小于80的浏览器预设 sameSite
    const isProduction = process.env.NODE_ENV === 'production';
    const inFrame = window.top !== window.self;
    const isNotSafari = !isSafari();
    const isChromePrevVersion = isChrome() && parseInt(getBroswer().version) < 80;
    if (
        isProduction &&
        inFrame &&
        isNotSafari
    ) {
        if (isChromePrevVersion) {
            return false;
        }
        const cookieSet = VueCookie.set;
        VueCookie.set = function(key, val, opt) {
            return cookieSet(key, val, { ...opt, sameSite: 'none', secure: isServer() ? true : null });
        };
        VueCookie.defaultOption = { sameSite: 'none', secure: isServer() ? true : null };
    }
}

function Cookie() {
    // 全站cookie增加Secure属性(https://jira.bestsign.tech/browse/CFD-12030)
    const cookieSet = VueCookie.set;
    VueCookie.set = function(key, val, opt) {
        return cookieSet(key, val, { ...opt, secure: isServer() ? true : null });
    };
    cookieOptionPreset();
    Vue.prototype.$cookie = VueCookie;
    Vue.$cookie = VueCookie;
    // 浏览器指纹
    new Fingerprint2().get(function(result) {
        const fingerPrint = result;
        Vue.$cookie.set('browser_fingerprint', fingerPrint);
    });
}
Vue.use(Cookie);

export default Cookie;
