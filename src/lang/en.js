
const components = {
    authorize: {
        name: 'Authorize', // 授权
        authorizeFile: 'File authorization', // 文件授权
        authorizeTip: 'You will need to give <PERSON><PERSON> permission to read and analyze the content of your contract files to utilize this feature.', // 您好，使用该功能需要授权上上签解析合同文件内容。
        authorizeAgree: 'Contract Authorization Agreement', // 《合同授权协议》
    },
    countDown: {
        resendCode: 'Resend', // 重新获取
        getVerifyCode: 'Get verification code', // 获取验证码
    },
    guide: {
        name: 'Guide', // 功能指引
        description1: 'Hubble, a refreshing approach to work.', // Hubble,一种全新的工作体验。
        description2: "Hello, I'm <PERSON><PERSON>, your dedicated intelligent assistant for contracts.", // 你好,我是哈勃,你的签约智能助手
        description3: 'An AI application with large model capabilities released by Shangshangqian, providing more efficient and convenient signing interaction capabilities.', // 一款由上上签发布的具有大模型能力的人工智能应用,提供更高效、便捷的签约互动能力。
        description4: 'We have prepared necessary usage introductions to help you get started quickly,', // 我们准备了必要的使用介绍来帮助快速上手,
        description5: 'You can easily explore the featured capabilities through the buttons below.', // 可以通过下方按钮轻松探索特色功能。
        startHubble: 'Start the Hubble journey now', // 即刻开启 Hubble 之旅
        newFileChat: 'New file chat', // 新建文档对话
        inputContent: 'Input content', // 输入对话内容
        underlineQuote: 'Phrase quotation', // 划词引用
        outputLanguage: 'Output language', // 输出内容语言
        suggestQuestions: 'Suggestions', // 建议问题
    },
    header: {
        accountConfig: 'Account settings', // 账号设置
        share: 'Share', // 分享
        myVersion: 'My version', // 我的版本
        shareHubble: 'Share Hubble document', // 分享 Hubble 智能文档
        inviteToHubble: {
            1: 'Invite you to participate in Hubble intelligent document', // 邀请您参与Hubble智能文档
            2: 'Document name:', // 合同名称:
            3: 'Click the link to enter directly:', // 点击链接直接进入:
            4: 'View password:', // 查看密码:
            5: 'Copy this info and share with others to invite them', // 复制该信息,分享给其他人即可
        },
        openClose: 'Open/Close sharing link, which will determine whether others can continue viewing the document', // 开启/关闭分享链接,将决定其他人是否可以继续查看文档
        inviteFriend: "Input friend's number to invite: ", // 输入好友手机号,邀请体验:

        inputFriendNumber: "Please input friend's number", // 请输入好友手机号
        sendInvite: 'Send invite', // 发送邀请
        packageDetail: 'Package details', // 套餐详情
        packageVersion: 'Package version:', // 套餐版本:
        currentPackage: 'Current package:', // 当前套餐:
        validDuration: '(Effective and expiration dates)', // (生效及截止日期)
        hasUsedNum: 'Used {num} documents', // 已用{num}份文档
        hasUsedTimes: 'Used {time} chats', // 已用{time}次对话
        totalNums: 'Total {num} documents', // 共{num}份文档
        totalTimes: 'Total {times} chats', // 共{times}次对话
        buyedPackage: 'Purchased package:', // 已购套餐:
        buyedContent: '({num} periods, package capacity same as above)', // ({num}期,套餐容量同上)
        invitation: 'Invite a friend',
    },
    helperFloat: {
        advice: 'Advice', // 咨询建议
        hide: 'Hide', // 隐藏
        suggest: 'Suggest', // 提建议
        onlineService: 'Online service', // 在线客服
        minimize: 'Minimize', // 最小化
        enlarge: 'Enlarge', // 放大
        zoomOut: 'Zoom out', // 缩小
        person: 'Person', // 个人
        ent: 'Enterprise', // 企业
        checkMoreVersion: 'Check more versions', // 查看更多版本
        submitSuccess: 'Submitted successfully', // 上报成功
        reportLog: 'Report log', // 上报日志
    },
    package: {
        myVersion: 'My version', // 我的版本
        tryVersion: 'Trial version', // 体验版
        free: 'Free', // Free / 免费
        packageDetail: 'Package includes:', // 套餐包含内容为:
        numFiles: 'documents', // 份文档
        timesChat: 'chats', // 次对话
        pagePerFile: '300 pages/document', // 300页/文档
        sizePerFile: '20MB/document', // 20MB/文档
        hasOpen: 'Activated', // 已开通
        validTo: 'Valid until', // 有效期至
        openNow: 'Activate now', // 立即开通
        personVersion: 'Personal version', // 个人版
        costPerMonth: '${price} / month', // ${price} / 月
        continueBuy: 'Renew package', // 续充套餐
        groupVersion: 'Team version', // 团队版
        connectUs: 'Contact us', // 联系我们
        connectTip: 'You can contact our professionals, we will provide custom solutions based on your situation.', // 可以联系我们的专业人员,我们会根据您的情况提供专属方案。
        hasRead: 'I have read and agree to the', // 我已阅读并同意
        agreenName: 'Document Authorization Agreement', // 《文档授权协议》
        readAgreeTip: 'Please read and agree to the Document Authorization Agreement first', // 请先阅读并同意《文档授权协议》
    },
    quickPay: {
        pleaseScanPay: 'Please scan to pay', // 请扫码支付
        openFun: 'Activate:', // 开通功能:
        payAmount: 'Payment amount:', // 支付金额:
        unit: 'RMB', // 元
        payTip: '1. Please scan to complete payment. You can apply for invoice in My Orders after payment.', // 1.请扫码完成支付,完成支付后可在个人中心-我的订单中申请发票。
        aliPay: 'Alipay', // 支付宝支付
        weixinPay: 'WeChat Pay', // 微信支付
        payOnline: 'Payment online',
        poweredBy: 'Powered by',
        pay: 'pay',
    },
};
const lang = {
    common: {
        tip: 'Tip', // 提示
        confirm: 'Confirm', // 确认
        confirmWS: 'Confirm', // 确 认
        cancel: 'Cancel', // 取消
        exit: 'Exit', // 退出
        send: 'Send', // 发送
        rename: 'Rename', // 重命名
        delete: 'Delete', // 删除
        copyInfo: 'Copy info', // 复制信息
        newName: 'New name', // 新名称
    },
    staticOpt: {
        goHome: 'Go home', // 返回首页
        downloadApp: 'Download Shangshangqian App', // 下载上上签APP
    },
    tips: {
        noData: 'No data', // 没有数据
        deleteSuccess: 'Deleted successfully', // 删除成功
        copySuccess: 'Copied successfully', // 复制成功
        openSuccess: 'Activated successfully', // 开通成功
        openFailed: 'Activated failed',
        renameSuccess: 'Renamed successfully', // 重命名成功
        packageNotEnough: 'Insufficient package balance', // 套餐余额不足
        uploadFailed: 'Upload failed', // 上传失败
        noMore: 'No more', // 没有更多了
        deleteChatTip: 'This will permanently delete the topic, continue?', // 此操作将永久删除该话题, 是否继续?
        pleaseOpenVersion: 'Please activate required version first', // 请先开通需要的版本
        pdfSupportOnly: 'Only PDF documents supported currently', // 目前仅支持PDF文档
        inputPhone: 'Please input phone number', // 请输入手机号
        inputVerifyCode: 'Please input verification code', // 请输入验证码
        lackAccount: 'Please fill in account before getting code', // 请填写账号后再获取
        pleaseCheckAuto: 'Please click the button for intelligent verification', // 请点击按钮进行智能验证
        notAccessPage: 'Unable to access this page', // 无法访问此页面
        page404: 'Page not found (404)', // 没有找到您访问的页面(404)
        systemOcupied: 'System busy, please try again later.', // 系统繁忙,请稍后再试。
        pageExpired: 'The page has expired, please refresh and try again.',
        networkError: 'Network error',
    },
    login: {
        welcome: 'Welcome to Hubble', // 欢迎使用Hubble
        welcomeTip: 'Intelligent signing assistant', // 智能签约助手
        name: 'Login', // 登录
        loginOrRegister: 'Login/Register', // 登录/注册
        hasReadAndAgree: 'I have read and agree to the', // 我已阅读并同意
        and: 'and', // 和
        bestsignAgreement: 'Shangshangqian Service Agreement', // 上上签服务协议
        digitalCertificateAgreement: 'Digital Certificate Usage Agreement', // 数字证书使用协议
        privacyPolicy: 'Privacy Policy', // 隐私政策
        autoRegisterForNewPhone: 'Unregistered phone number will be automatically registered after verification', // 未注册手机号验证后会自动创建账号
        sendSuc: 'Sent successfully', // 发送成功
    },
    upload: {
        name: 'Upload document', // 上传文档
        selectFile: 'Select document', // 选择文档
        dragToUpload: 'Drag document here to upload', // 将文档拖拽至此上传
        pdfOnly: 'Only PDF documents supported currently', // 目前仅支持PDF文档
        releaseMouseToUpload: 'Release mouse to complete upload', // 释放鼠标完成上传
    },
    share: {
        inviteToUse: 'Invite you to participate in Hubble intelligent document', // 邀请您参与Hubble智能文档
        fileName: 'Document name:', // 文档名称:
        inputShareCodeToView: 'Input share code to view document:', // 填写分享码查看文档:
        inputShareCodeTip: 'Please input 6-digit share code', // 请输入6位数字分享码
    },
    chat: {
        switchOutputLanguage: 'Switch output language', // 输出语言切换
        chinese: 'Chinese', // 中文语言
        english: 'English', // 英文语言
        selectAndClickAsk: 'Select text and click to ask question', // 选中文字后点击提问
        downloadFile: 'Download document', // 文档下载
        sourceFile: 'Source file', // 源文件
        sourceFileWithChat: 'Source file (with chat)', // 源文件 (携带对话)
        fileAnalyzing: 'Analyzing document', // 文档解析中
        continuousChat: 'Continuous chat mode:', // 连续对话模式:
        quoteContent: 'Quoted content:', // 引用内容:
        suggestionsQuestions: 'Suggested questions', // 建议问题
        inputQuestionTip: 'Please input your question', // 请输入您的问题

        selectToQuestTip: 'Input below or select text in document then click "Hubble" to ask questions', // 在下方输入或在合同上选取后点击“Hubble”,进行提问
        question: 'Question:', // 问题:
        explainTip: '~ The above is AI generated and does not represent Shangshangqian\'s position, for reference only, please do not delete or modify this label', // ~ 以上内容为AI生成,不代表上上签立场,仅供您参考,请勿删除或修改本标记
        relatedContent: 'Related content:', // 相关内容:
        deleteConversation: 'Delete conversation', // 删除对话
        openContinuousModel: 'Enable continuous chat', // 开启连续对话
        confirmToDelete: 'Confirm deleting this conversation?', // 确认删除该对话吗?
    },
    workspace: {
        create: 'Created',
        reviewing: 'Reviewing',
        completed: 'Completed',
        noData: 'Empty',
        introduce: 'Meaning of the {keyword}',
        termsDetail: 'Details',
        extractFormat: 'Format',
        optional: 'Optional',
        required: 'Required',
        operate: 'Operation',
        detail: 'Details',
        delete: 'Delete',
        agreement: {
            uploadError: 'Only PDF, DOC, or DOCX files can be uploaded!',
            extractionRequest: 'Extraction request has been submitted. Please check the results in the terms list later.',
            upload: 'Upload',
            define: 'Defination',
            extract: 'Extraction',
            drag: 'Drag the file here or',
            add: 'click',
            format: 'Supported file formats are doc,docx,pdf',
            fileName: 'FileName',
            status: 'Status',
            completed: 'Completed',
            failed: 'Failed',
            size: 'Size',
            terms: 'Terms',
            success: 'The extraction has been completed, totaling {total}',
            ongoing: 'In progress...totaling {total}',
            tips: 'Skipping this page does not affect the results of the extraction',
            others: 'Upload other agreements',
            result: 'Jump to the download page of the extraction result',
            curProgress: 'Current progress: ',
            refresh: 'Refresh',
            details: 'Loaded {successNum}，totaling {length}',
            start: 'Start',
            more: 'Add',
            skip: 'Skip',
            tiqu: 'Start',
            chouqu: 'Start',
        },
        review: {
            distribution: 'Distribution review',
            Incomplete: 'Incomplete',
            createReview: 'Create Review',
            manageReview: 'Review Management',
            reviewDetail: 'Review Details',
            reviewId: 'Review ID',
            reviewStatus: 'Review Status',
            reviewName: 'Review Name',
            reviewStartTime: 'Review Start Time',
            reviewCompleteTime: 'Review Complete Time',
            reviewDesc: 'Version：V.{reviewVersion} | ReviewId：{reviewId}',
            distribute: 'Initiate Review',
            drag: 'Drag the agreement to be reviewed here',
            content: 'Content',
            current: 'Current',
            history: 'History',
            page: 'Page {x}：',
            users: 'Users：',
            message: 'Message',
            modify: 'Modify',
            placeholder: 'Please use semicolons to separate multiple users',
            submit: 'Submit',
            reupload: 'Reupload',
            finish: 'Finish',
            reviewSummary: 'Review Summary',
            initiator: 'Initiator',
            versionSummary: 'Version Summary',
            version: 'Version',
            versionOrder: 'Version {version}',
            curReviewStatus: 'Current Version’s Status',
            curReviewVersion: 'Current Version',
            curReviewPopulation: 'Current Version’s Review Population',
            curReviewStartTime: 'Current Version’s Review Start Time',
            curReviewInitiator: 'Current Version’s Review Initiator',
            checkComments: 'Check Comments',
            overview: 'Overview',
            reviewer: 'Reviewer',
            reviewResult: 'Review Result',
            replyTime: 'Reply Time',
            agreement: 'Agreements',
            files: 'Files',
            fileName: 'FileName',
            numberOfModificationSuggestions: 'Number of modification suggestions',
            uploadTime: 'Upload Time',
            download: 'Download',
            dispatch: 'Dispatch',
            recent: 'Latest review time：',
            replyContent: 'Reply Content',
            advice: 'Advice',
            noIdea: 'Have no advice',
            origin: 'Original Content: ',
            revised: 'Revised Content',
            suggestion: 'Suggestion: ',
            revisionFiles: 'Revision Files',
            staffReplyAggregation: 'Overview',
            staffReply: '{name}’s Comments',
            tips: 'Tips',
            tipsContent: 'If you are sure to perform this operation, this review will no longer support distribution and subsequent operations. Do you want to continue?',
            confirm: 'Confirm',
            cancel: 'Cancel',
            successMessage: 'Completed',
            PASS: 'Pass',
            NOT_PASS: 'Not Pass',
            uploadErrorMessage: 'Currently, only DOCX format files are supported for upload.',
            successInitiated: 'Review initiated',
        },
        contentTracing: {
            title: 'Content Tracing',
            fieldContent: 'Field Content',
            originalResult: 'Original Result',
            contentSource: 'Content Source',
            page: 'Page',
        },
    },
    pdf: {
        previewFail: 'File preview failed',
        pager: 'Page {x}，{y} in total',
        parseFailed: 'Failed to parse the pdf file, please click "OK" to try again',
        confirm: 'Confirm',
    },
    landing: {
        navFeatures: 'Features',
        navScenarios: 'Use cases',
        navAdvantages: 'Advantages',
        heroTitle: 'Hubble: Unleash the power of intelligent contract management',
        heroSubtitle: 'Transform your operations with AI-driven contract analysis, workflow optimization, and automated document generation.',
        ctaStart: 'Get started',
        ctaLearnMore: 'Learn more',
        featuresTitle: 'Core capabilities',
        f1Title: 'Intelligent contract analysis',
        f1Desc: 'Extract key business insights from contracts and uncover opportunities with AI.',
        f2Title: 'Workflow optimization',
        f2Desc: 'Streamline your workflows and reduce operational inefficiencies.',
        f3Title: 'Automated document generation',
        f3Desc: 'Auto-create and customize contracts and reports to your business needs.',
        scenariosTitle: 'Use case scenarios',
        s1Title: 'Financial data extraction',
        s1Desc: 'Quickly extract and analyze key financial data from various contracts to support decisions.',
        s2Title: 'Supplier relationship management',
        s2Desc: 'Optimize supplier contracts and tracking to improve supply chain efficiency.',
        s3Title: 'M&A due diligence',
        s3Desc: 'Analyze large volumes of contracts to accelerate due-diligence insights.',
        advantagesTitle: 'Business advantages',
        a1Title: 'Cost reduction',
        a1Desc: 'Reduce operating costs through efficient management and automation.',
        a2Title: 'Revenue growth',
        a2Desc: 'Identify new revenue opportunities and optimize existing contracts.',
        a3Title: 'Stakeholder satisfaction',
        a3Desc: 'Simplify processes and improve relationships with customers and partners.',
        bottomCtaTitle: 'Ready to transform your contract management?',
    },
    contractCompare: {
        reUpload: 'Re-upload',
        title: 'Contract Comparison',
        packagePurchase: 'Plan Purchase',
        packagePurchaseTitle: '[{title}] Plan Purchase',
        myPackage: 'My Plan',
        packageDetail: 'Plan Details',
        per: '次',
        packageContent: 'Plan Includes：',
        num: '{type}次数',
        limitTime: 'Validity Period',
        month: 'month',
        payNow: 'Buy Now',
        contactUs: 'Contact Us | Scan QR Code to Consult Professional Advisor',
        compareInfo1: 'Usage Instructions：',
        compareInfo2: '{index}、The available quota for {type} of purchases is accessible to all members of the enterprise. If you only require it for personal use, you may switch to a personal account by changing the login entity in the upper right corner.',
        compareInfo3: '{index}、Usage Calculated Based on Number of Uploaded Contract {per}',
        codePay: 'Please Scan QR Code to Pay',
        aliPay: 'Alipay Payment',
        wxPay: 'WeChat Payment',
        payIno: 'Activated Features | Purchased For | Payment Amount',
        finishPay: 'Payment Completed',
        paySuccess: 'Purchase Successful',
        originFile: 'Original Contract File',
        compareFile: 'Contract File for Comparison',
        documentSelect: 'Select File',
        comparisonResult: 'Comparison Result',
        history: 'History',
        currentHistory: 'Document Records',
        noData: 'No Data Available',
        differences: '{num} Differences',
        historyLog: '{num} Records',
        uploadLimit: 'Drag & Drop Files to Compare | Supported Formats: PDF (including scanned), Word',
        dragInfo: 'Release Mouse to Upload',
        uploadError: 'Unsupported File Format',
        pageNum: '第{page}页',
        difference: 'Differences {num}',
        download: 'Download Comparison Result',
        comparing: 'Comparing Contracts...',
        tip: 'Notification',
        confirm: 'Confirm',
        toBuy: 'Go to Purchase',
        translate: 'Contract Translation',
        doCompare: 'Compare',
        doTranslate: 'Translate',
        review: 'Contract Review',
        doReview: 'Review',
        reviewUploadFile: 'Drag Files to be Reviewed Here',
        reviewUpload: 'Drag Review Reference Files Here | e.g., "Distributor Management Policy", "Procurement Regulations" | Supported Formats: PDF, Word',
        reviewOriginFile: 'Contract Under Review',
        reviewTargetFile: 'Review Basis',
        reviewResult: 'Review Result',
        uploadReviewFile: 'Upload Reference Files for Review',
        risk: 'Risk Point {num}',
        risks: '{num} Risk Point(s)',
        startReview: 'Start Review',
        reviewing: 'Reviewing Contract...',
        noRisk: 'Review Completed - No Risks Detected',
        allowUpload: 'You can upload company regulations (e.g., "Procurement Management Policy"), compliance guidelines, or departmental instructions to guide contract review. | Example: "Party A must complete payment within 5 days after contract signing."',
        notAllowUpload: 'Avoid vague or general statements as review basis. | Example: "All contract terms must comply with applicable laws and regulations."',
        resumeReview: 'Continue to Next File',
        close: 'Close',
        extract: 'Contract Extraction',
        extractTitle: 'Keywords to Extract',
        selectKeyword: 'Select Keywords from the List Below',
        keyword: 'Keywords',
        addKeyword: 'Add{keyword}',
        introduce: '{keyword} Definition',
        startExtract: 'Start Extraction',
        extractTargetFile: 'Contract for Extraction',
        extractKeyWord: 'Extract Keywords',
        extracting: 'Extracting Contract...',
        extractResult: 'Extraction Result',
        extractUploadFile: 'Drag & Drop Files for Extraction Here',
        needExtractKeyword: 'Select Keywords to Extract',
        summary: 'Contract Summary',
        keySummary: 'Keyword Summary',
        deleteKeywordConfirm: 'Confirm Deletion of this Keyword?',
        keywordPosition: 'Keyword Locations',
        riskJudgement: 'Risk Assessment',
        judgeTargetContract: 'Contract Under Assessment',
        interpretTargetContract: 'AI-Interpreted Contracts',
        startJudge: 'Start',
        startInterpret: 'Start Now',
        uploadText: 'Please upload the documents for risk assessment',
        interpretText: 'Please upload documents for AI analysis',
        startTips: 'Now we can start to judge the risks',
        interpretTips: 'Now we can start to interpret the document',
        infoExtract: 'Extract information',
    },
    judgeRisk: {
        title: 'AI Lawyer',
        deepInference: 'AI Lawyer(Deep Inference)',
        showAll: 'Show More',
        tips: 'Judging',
        dialogTitle: '“AI Lawyer” Reviews Contracts',
        aiInterpret: 'AI Interpretation',
    },
    keyInfoExtract: {
        operate: 'Extract Information',
        contractType: 'Predicted Contract Types',
        tooltips: 'Select the Key Information',
        predictText: 'Predicting',
        extractText: 'Extracting',
        errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',
        result: 'result:',
    },
    agent: {
        extractTitle: 'Information Extraction',
        riskTitle: 'AI Lawyer',
        feedback: 'Questionnaire',
        toMini: 'Check Details in App',
        otherContract: 'Analyze latent risks in other contracts?',
        others: 'Others',
        submit: 'Submit',
        autoExtract: 'Automatic Extraction Until Completion',
        autoRisk: 'Auto-Analysis Process Running',
        aiGenerated: 'AI Generated - © BestSign',
        chooseRisk: 'Select File for Analysis',
        chooseExtract: 'Specify Source File for Extraction',
        analyzing: 'Analyzing',
        advice: 'Generating Revision Suggestions',
        options: 'Generating Options',
        inputTips: 'Enter Precise Information',
        chargeTip: 'Insufficient Balance - Top Up Required',
        original: 'Original',
        revision: 'Revision',
        diff: 'Comparison',
        locate: 'Locating',
        custom: 'Please enter custom review rules',
        content: 'Original text location',
        satisfy: 'Satisfied with analysis results, continue to next analysis',
        dissatisfy: 'Not satisfied with analysis results, re-analyze',
        selectFunc: 'Please select the function you want to use.',
        deepInference: 'AI Lawyer (Deep Reasoning)',
        deepThinking: 'Deep thinking',
        deepThoughtCompleted: 'Deep thinking completed',
        reJudge: 'Re-judge',
        confirm: 'Confirm',
        tipsContent: 'Re-judging will deduct usage count, continue?',
        useLawyer: 'Use AI Lawyer',
        interpretFinish: 'AI interpretation completed',
        paySuccess: 'Payment successful',
        payFail: 'Payment failed',
        payCanceled: 'Payment canceled',
        paymentProcessing: 'Payment Processing',
        paymentPendingTip: 'Your payment is being processed, please wait patiently. We will check the payment status regularly and update automatically once completed.',
        recheckPayment: 'Check Again',
        checkPaymentFailed: 'Failed to check payment status, please try again later',
        custom: 'Please enter custom review rules',
        content: 'Locate Original Text',
        satisfy: 'Satisfied with the analysis results, continue to the next analysis',
        dissatisfy: 'Not satisfied with the analysis results, re-analyze',
        selectFunc: 'Please select the function you wish to use',
        deepInference: 'AI Lawyer (Deep Inference)',
        deepThinking: 'Deep Thinking',
        deepThoughtCompleted: 'Deep Thinking Completed',
        reJudge: 'Re-judge',
        confirm: 'Confirm',
        tipsContent: 'Proceeding will deduct from your usage credits. Continue?',
        useLawyer: 'Use AI Lawyer',
        interpretFinish: 'AI Analysis Complete',
    },
    hubblePackage: {
        title: 'My Package',
        details: 'Package Details',
        remainingPages: 'Remaining Total Pages',
        pages: 'Pages',
        usedPages: 'Used',
        remaining: 'Available Remaining',
        total: 'Total',
        expiryTime: 'Expiry Time',
        amount: 'Quantity',
        unitPrice: 'Each',
        copy: 'Copy',
        words: 'Thousand Characters',
    },
    commonFooter: {
        record: 'ICP main body record number: Zhejiang ICP No. 14031930',
        hubbleRecordId: '网信算备：330106973391501230011',
        openPlatform: 'Open platform',
        aboutBestSign: 'About BestSign',
        contact: 'Contact us',
        recruitment: 'Recruitment',
        help: 'Help Center',
        copyright: 'Copyright',
        company: 'HangZhou BestSign Ltd.',
        ssqLogo: 'BestSign bottom bar logo',
        provideTip: 'E-signing service is provided by',
        ssq: ' BestSign',
        provide: '',
        signHotline: 'Service hotline',
        langSwitch: 'Language',
    },
    ...components,
};
const workspace = {
    workspaceIndex: {
        title: 'Workspace',
        package: 'Package Usage',
        agreement: 'Agreement',
        review: 'Review',
        term: 'Term',
    },
    agreement: {
        title: 'Agreement Management',
        exportList: 'Export Agreement List',
        exportAllChecked: 'Excel (All Fields, Selected Agreements)',
        exportCurrentChecked: 'Excel (Current Fields, Selected Agreements)',
        exportAllMatched: 'Excel (All Fields, Matched Conditions)',
        exportCurrentMatched: 'Excel (Current Fields, Matched Conditions)',
        add: 'Add Agreement',
        upload: 'Upload Agreement',
        operation: 'Operation',
        download: 'Download Agreement',
        details: 'Details',
        delete: 'Delete',
        relatedTaskStatus: 'Related Extraction Task Status',
        confirmDelete: 'Are you sure to delete the current agreement?',
        prompt: 'Prompt',
        booleanYes: 'Yes',
        booleanNo: 'No',
        defaultExportName: 'export.xlsx',
        taskNotStarted: 'Extraction Task Not Started',
        taskStarted: 'Extraction Task Started (Content Searching)',
        contentSearchCompleted: 'Content Search Completed (Formatting Results)',
        resultFormattingCompleted: 'Result Formatting Completed (Verifying Results)',
        resultVerificationCompleted: 'Result Verification Completed',
    },
    filter: {
        filter: 'Filter',
        refreshExtraction: 'Refresh Extraction',
        extractTerms: 'Extract Term Definitions',
        refreshList: 'Refresh List',
        currentCondition: 'Displaying agreements under current conditions.',
        when: 'When',
        selectCondition: 'Please Select Condition',
        enterCondition: 'Please Enter Condition',
        yes: 'Yes',
        no: 'No',
        addCondition: 'Add Condition',
        reset: 'Reset',
        confirm: 'Confirm',
        and: 'And',
        or: 'Or',
        equals: 'Equals',
        notEquals: 'Not Equals',
        contains: 'Contains',
        notContains: 'Does Not Contain',
        greaterThan: 'Greater Than',
        greaterThanOrEquals: 'Greater Than or Equals',
        lessThan: 'Less Than',
        lessThanOrEquals: 'Less Than or Equals',
        emptyCondition: 'Filter condition cannot be empty',
    },
    fieldConfig: {
        button: 'Field Configuration',
        header: 'Fields to be displayed',
        submit: 'Submit',
        cancel: 'Cancel',
    },
    agreementDetail: {
        detail: 'Agreement Details',
        add: 'Add Agreement',
        id: 'Agreement ID',
        file: 'Agreement File',
        download: 'Download Agreement',
        replaceFile: 'Replace Agreement File',
        uploadFile: 'Upload Agreement File',
        relatedExtractionStatus: 'Related Extraction Task Status',
        dataSource: 'Data Source',
        yes: 'Yes',
        no: 'No',
        select: 'Please Select',
        input: 'Please Input',
        save: 'Save',
        cancel: 'Cancel',
        page: 'Page {page}',
        addDataSource: 'Add Data Source',
        pageNo: 'Page',
        pageSuffix: '',
        submit: 'Submit',
        inputDataSource: 'Please input data source content',
        pageFormatError: 'Page number only supports comma-separated numbers or pure numbers',
        confirmDelete: 'Are you sure to delete the current data source?',
        tips: 'Tips',
        uploadSuccess: 'Upload Successful',
    },
    termManagement: {
        title: 'Term Management',
        batchDelete: 'Batch Delete',
        import: 'Import Terms',
        export: 'Export Terms',
        add: '+ Add Term',
        name: 'Term Name',
        definition: 'Term Definition',
        formatRequirement: 'Extraction Format Requirement',
        dataFormat: 'Data Format',
        operation: 'Operation',
        edit: 'Edit',
        delete: 'Delete',
        detail: 'Term Details',
        addTitle: 'Add Term',
        namePlaceholder: 'Enter your specialized term',
        definitionPlaceholder: 'Enter the definition of the term',
        formatRequirementPlaceholder: 'Enter the extraction format requirement of the term',
        dataFormatPlaceholder: 'Expected extracted term format',
        cancel: 'Cancel',
        confirmEdit: 'Confirm Edit',
        importTitle: 'Import Terms',
        uploadTemplate: 'Upload Term Template',
        downloadTemplate: 'Download Term Template',
        extractType: {
            text: 'Text',
            longText: 'Long Text',
            date: 'Date',
            number: 'Number',
            boolean: 'Yes/No',
        },
        importSuccess: 'Import Successful',
        deleteConfirm: 'Are you sure to delete the current term?',
        prompt: 'Prompt',
        nameEmptyError: 'Term name cannot be empty',
    },
};
export default {
    ...lang,
    ...workspace,
    lang: 'en',
};
