import Vue from 'vue';
/** 比对 start */
export function getDocumentPageSize(contractId, documentId) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}/documents/${documentId}/total-page-count`);
}

export function getComparisonTaskStatus(contractCompareEventId) {
    return Vue.$http.get(`/web/document-compare/documents/contract-compare-event/${contractCompareEventId}/task-status`);
}

export function getComparisonDifferences(contractCompareEventId) {
    return Vue.$http.get(`/web/document-compare/documents/contract-compare-event/${contractCompareEventId}/difference-items`);
}

export function startCompare(contractCompareEventId) {
    return Vue.$http.post('/web/document-compare/documents/compare', { contractCompareEventId });
}

export function getHistory() {
    return Vue.$http.get('/web/document-compare/documents/contract-compare-event/history-record');
}

export function calcBilling(toolType, originFileId, comparisonFileId) {
    return Vue.$http.post('/web-api/users/tool/calculate-billing', { toolType, originFileId, comparisonFileId });
}

export function getContractCompareInfo(contractId, documentId) {
    return Vue.$http.get(`/web/document-compare/documents/${contractId}/document/${documentId}`);
}

export function initTask() {
    return Vue.$http.get(`/web/document-compare/documents/init`);
}

export function newCalcBilling(compareEventId) {
    return Vue.$http.get(`/web-api/users/tool/calculate-billing/compare/${compareEventId}`);
}

export function getCurrentDocHistory(contractId, documentId, hubbleToolType) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}/document/${documentId}/hubble-tasks`, {
        params: {
            hubbleToolType,
        },
    });
}

export function getSummary(compareEventId) {
    return Vue.$http.get(`/web-api/contract-compare/${compareEventId}/get-summary`);
}

export function summarize(compareEventId) {
    return Vue.$http.post(`/web-api/contract-compare/${compareEventId}/summarize`);
}

/** 比对 end */

/** 充值套餐 start */
export function getChargePackage() {
    return Vue.$http.get('/ents/charging/hubble-app/package');
}

export function getChargeOrder(id) {
    return Vue.$http.post(`/ents/charging/package/${id}/ordering`);
}

export function checkPay(id) {
    return Vue.$http.get(`/ents/charging/rechargeRecord/order/${id}`);
}

export function getWeiXinPayImg(id) {
    return Vue.$http.post(`/ents/wechat/request/${id}`);
}

export function getAliPayImg(id) {
    return Vue.$http.get(`/ents/alipay/request/${id}`);
}

export function getPackageDetail(toolType) {
    return Vue.$http.get('web/hubble/users/tool/overview-plan-detail', {
        params: {
            toolType,
        },
    });
}
/** 充值套餐 end */
