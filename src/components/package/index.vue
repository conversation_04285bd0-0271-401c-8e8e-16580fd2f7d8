<template>
    <div>
        <!-- 购买弹窗 -->
        <QuickPay
            v-if="showPayDialog"
            v-model="showPayDialog"
            type="hubble"
            :packageInfo="packageInfo"
            @handlePaySuccess="handlePaySuccess"
            @handlePayCancel="handleClose"
        >
        </QuickPay>
        <el-dialog
            v-else
            class="hubble-component__package"
            :visible.sync="value"
            :title="$t('package.myVersion')"
            :before-close="handleClose"
            :close-on-click-modal="false"
        >
            <ul class="hubble-component__package-list">
                <li class="hubble-component__package-item" v-if="showFreePackage">
                    <div class="hubble-component__package-header free">
                        <div class="hubble-component__package-header__title">{{ $t('package.tryVersion') }}</div>
                        <span class="hubble-component__package-header__price">{{ $t('package.free') }}</span>
                    </div>
                    <div class="hubble-component__package-content">
                        <span>{{ $t('package.packageDetail') }}</span><br>
                        <span> - {{ freePackage.documentCount }}{{ $t('package.numFiles') }}</span><br>
                        <span> - {{ freePackage.chatCount }}{{ $t('package.timesChat') }}</span><br>
                        <span> - {{ $t('package.pagePerFile') }}</span><br>
                        <span> - {{ $t('package.sizePerFile') }}</span><br>
                    </div>
                    <div class="hubble-component__package-status" v-if="freePackage.isOpen">
                        <b>{{ $t('package.hasOpen') }}</b>
                        {{ $t('package.validTo') }} {{ freePackage.expireTime }}
                    </div>
                    <el-button v-else :disabled="turnonLoading" type="primary" @click="handleTurnon('free')">{{ $t('package.openNow') }}</el-button>
                </li>
                <li class="hubble-component__package-item">
                    <div class="hubble-component__package-header person">
                        <div class="hubble-component__package-header__title">{{ $t('package.personVersion') }}</div>
                        <s>${{ personPackage.originPrice }}</s>&nbsp;
                        <span class="hubble-component__package-header__price">{{
                            $t('package.costPerMonth', {price: personPackage.currentPrice}) }}</span>
                    </div>
                    <div class="hubble-component__package-content">
                        <span>{{ $t('package.packageDetail') }}</span><br>
                        <span> - {{ personPackage.documentCount }}{{ $t('package.numFiles') }}</span><br>
                        <span> - {{ personPackage.chatCount }}{{ $t('package.timesChat') }}</span><br>
                        <span> - {{ $t('package.pagePerFile') }}</span><br>
                        <span> - {{ $t('package.sizePerFile') }}</span><br>
                    </div>
                    <template v-if="personPackage.isOpen">
                        <el-button type="primary" @click="handleTurnon('person')">{{ $t('package.continueBuy') }}</el-button>
                        <div class="hubble-component__package-status">{{ $t('package.validTo') }} {{ personPackage.expireTime }}</div>
                    </template>
                    <el-button v-else type="primary" @click="handleTurnon('person')">{{ $t('package.openNow') }}</el-button>
                </li>
                <!-- <li class="hubble-component__package-item">
                    <div class="hubble-component__package-header group">
                        <div class="hubble-component__package-header__title">{{ $t('package.groupVersion') }}</div>
                        <span class="hubble-component__package-header__price">{{ $t('package.connectUs') }}</span>
                    </div>
                    <div class="hubble-component__package-content">
                        {{ $t('package.connectTip') }}
                    </div>
                    <el-button type="primary" @click="handleContact">{{ $t('package.connectUs') }}</el-button>
                </li> -->
            </ul>
            <!-- <template v-if="!hasAuthorized">
                <el-checkbox v-model="agreeAuthorize">{{ $t('package.hasRead') }}</el-checkbox> <a class="instruction" target="_blank" href="/哈勃产品使用须知.pdf">{{ $t('package.agreenName') }}</a>
            </template> -->
        </el-dialog>

    </div>
</template>

<script>
import JSConfetti from 'js-confetti';
import { mapMutations } from 'vuex';
import QuickPay from 'src/components/QuickPay';
export default {
    components: {
        QuickPay,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        currentPackage: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            loading: false,
            packageData: {},
            // hasAuthorized: false,
            agreeAuthorize: false,
            showPayDialog: false,
            turnonLoading: false,
            packageInfo: {},
        };
    },
    computed: {
        freePackage() {
            return this.packageData.trialPlanInfo || {};
        },
        personPackage() {
            return this.packageData.personBasicPlanInfo || {};
        },
        showFreePackage() {
            return !this.personPackage.isOpen;
        },
    },
    watch: {
        async currentPackage(val) {
            await this.initPackageList();
            Object.keys(this.packageData).forEach(key => {
                if (this.packageData[key].planType === val.planType) {
                    this.packageData[key] = {
                        ...this.packageData[key],
                        ...val,
                        isOpen: true,
                        expireTime: val.planOverviewExpireTime,
                    };
                }
            });
        },
    },
    methods: {
        ...mapMutations('hubble', ['togglePackageDialog']),
        initPackageList() {
            return this.$http('/web-api/users/aggregation/plan-basic-detail').then(res => {
                this.packageData = res.data;
            });
        },
        // getAuthorizeStatus() {
        //     this.$http('/web-api/users/permissions').then(({ data: { ifHubbleChatAuthorized } }) => {
        //         this.hasAuthorized = ifHubbleChatAuthorized;
        //     });
        // },
        handleClose() {
            this.togglePackageDialog(false);
            this.interval && clearInterval(this.interval);
            this.showPayDialog = false;
        },
        handleContact() {
            this.$featureSupport('hubble');
        },
        async handleTurnon(type) {
            // if (!this.hasAuthorized && !this.agreeAuthorize) {
            //     return this.$MessageToast.error(this.$t('package.readAgreeTip'));
            // }
            if (type === 'free') {
                await this.turnonFree();
            } else {
                await this.turnonPerson();
            }
            // !this.hasAuthorized && await this.handleAuthorize();
            // this.getAuthorizeStatus();
        },
        handleAuthorize() {
            return this.$http.post('/web-api/users/permissions/agree-authorization-letter');
        },
        turnonFree() {
            this.turnonLoading = true;
            return this.$http.post('/web-api/users/trial-account-init').then(() => {
                this.handlePaySuccess(true);
            }).finally(() => {
                this.turnonLoading = false;
            });
        },
        turnonPerson() {
            this.packageInfo = this.personPackage;
            this.showPayDialog = true;
        },
        handlePaySuccess(isShow) {
            this.handleClose();
            this.$emit('handleNext');
            if (isShow) {
                const confetti = new JSConfetti();
                confetti.addConfetti().then(() => {
                    this.$MessageToast.success(this.$t('tips.openSuccess'));
                });
            }
        },
    },
    created() {
        // !this.$route.meta.isSharePage && this.getAuthorizeStatus();
    },
};
</script>

<style lang="scss">
.hubble-component__package{
    .el-dialog{
        width: unset !important;
        border-radius: 4px !important;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        .el-dialog__body{
            padding: 30px !important;
        }
    }
    &-pay{
        display: flex;
        flex-direction: row;
        &__img{
            width: 180px;
            height: 180px;
            margin-right: 30px;
            border: 1px solid #ccc;
        }
        &__tips li{
            list-style-type: auto;
        }
    }
    &-list{
        display: flex;
        margin-bottom: 35px;
    }
    &-item{
        width: 220px;
        height: 320px;
        box-sizing: border-box;
        border: 1px solid rgba(234,234,234,1);
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        cursor: default;
        & + .hubble-component__package-item{
            margin-left: 20px;
        }
        &:hover{
            border-color: #0C8AEE;
            box-shadow: 0px 0px 10px 0px rgba(12,138,238,0.2);
        }
    }
    &-header{
        height: 88px;
        padding: 25px 25px 0;
        box-sizing: border-box;
        &.free{
            background-image: linear-gradient(116deg, #FBFBFB 0%, #D3D3D3 100%);
        }
        &.person{
            background-image: linear-gradient(113deg, #C5E1FF 0%, #80BCFF 100%);
        }
        &.group{
            background-image: linear-gradient(112deg, #696969 0%, #2C2C2D 100%);
            .hubble-component__package-header__title, .hubble-component__package-header__price{
                color: #FFFFFF;
            }
        }
        &__title{
            font-size: 18px;
        }
        &__price{
            color: #0C8AEE;
        }
    }
    &-content{
        font-size: 12px;
        color: #666666;
        padding: 25px;
        line-height: 24px;
    }
    &-status{
        width: 100%;
        font-size: 12px;
        text-align: center;
        position: absolute;
        bottom: 5px;
        b{
            display: block;
            font-size: 14px;
            margin-bottom: 10px;
        }
    }
    .el-button{
        width: 180px;
        line-height: 38px;
        padding: 0;
        position: absolute;
        bottom: 25px;
        left: calc((100% - 180px) / 2);
        text-align: center;
        border-radius: 4px;
    }
    a.instruction:hover{
        text-decoration: underline;
    }
}
</style>
