<template>
    <div class="hubble-document">
        <div class="hubble-document__header">
            <div class="hubble-document__title">
                {{ documentInfo.fileName }}
            </div>
            <div class="hubble-document__scale-box">
                <i class="iconfont el-icon-ssq-suoxiaojing scale" @click="handleScale('minus')"></i>
                <i class="iconfont el-icon-ssq-fangdajing1 scale" @click="handleScale('plus')"></i>
                <i class="el-icon-ssq-jiantou1" @click="handlePage('pre')"></i>
                <el-input v-model="currentPage" @keyup.enter.native="scrollTo" @blur="scrollTo"></el-input>
                <span><i>/</i>  <em>{{ documentInfo.pageSize }}</em></span>
                <i class="el-icon-ssq-jiantou1" @click="handlePage('next')"></i>
            </div>
        </div>
        <el-tooltip :open-delay="500" effect="dark" :content="$t('chat.selectAndClickAsk')" placement="top">
            <div :id="showQuote ? '' : 'guide-quote'" class="hubble-document__quote fixed" v-show="!showQuote"></div>
        </el-tooltip>
        <div class="hubble-document__content" ref="documentContent" @scroll="handleScroll">
            <div class="hubble-document__content-loading" v-if="pdfLoading">
                <img src="~img/loading.gif" alt="">
            </div>
            <div class="hubble-document__content-box" ref="pdfContainer">
                <el-tooltip :open-delay="500" effect="dark" :content="$t('chat.selectAndClickAsk')" placement="top">
                    <div :id="!showQuote ? '' : 'guide-quote'" class="hubble-document__quote" v-show="showQuote" :style="quoteStyle" @click.stop="handleQuote"></div>
                </el-tooltip>
            </div>
        </div>
    </div>
</template>

<script>
import 'pdfview/build/pdf.js';
import 'pdfview/web/pdf_viewer';
import 'pdfview/web/pdf_viewer.css';
import workerSrc from 'pdfview/build/pdf.worker.js';
import { mapMutations, mapState } from 'vuex';
import { throttle } from 'utils/fn.js';

PDFJS.workerSrc = workerSrc;
PDFJS.disableWorker = true;
// PDFJS.cMapUrl = pdfCmaps;
PDFJS.cMapPacked = true;
export default {
    props: {
        topicId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            // DPR: window.devicePixelRatio || 1,
            DPR: 2,
            maxWidth: 0,
            preContent: '',
            preCoordinate: {},
            textLayer: null,
            showQuote: false,
            quoteTop: null,
            quoteLeft: null,
            selectedArea: null,
            currentPage: 1,
            pageHeights: [],
            scale: 1,
            selectedIndex: 0,
            scrollDisabled: false,
            pdfLoading: true,
        };
    },
    computed: {
        ...mapState('hubble', ['startSelectQuote', 'preQuote', 'quotePage', 'quoteCoordinate', 'showSlider', 'documentInfo']),
        pdfUrl() {
            return `/web-api/topic/${this.topicId}/documents/${this.documentInfo.documentId}/download?access_token=${this.$cookie.get('access_token')}`;
        },
        quoteStyle() {
            return {
                '--quote-top': this.quoteTop,
                '--quote-left': this.quoteLeft,
            };
        },
    },
    watch: {
        scale() {
            setTimeout(() => {
                this.$refs.pdfContainer.style.width = `${this.maxWidth * this.scale}px`;
            }, 1000);
        },
        startSelectQuote(val) {
            if (val) {
                this.clearSelectedArea();
                const parent = document.querySelector(`#page-${this.quotePage}`);
                const { offsetHeight, offsetWidth, offsetTop } = parent;
                const { x, y, width, height } = this.quoteCoordinate;
                const realLeft = x * offsetWidth;
                const realTop = offsetHeight * y;
                const realWidth = width * offsetWidth;
                const realHeight = height * offsetHeight;
                this.currentQuoteArea = parent.querySelector('.hubble-document__selected');
                this.currentQuoteArea.style = `top: ${realTop}px; left: ${realLeft}px; width: ${realWidth}px; height: ${realHeight}px; display: block;`;
                this.$refs.documentContent.scrollTo({
                    top: realTop + offsetTop - 100,
                    behavior: 'smooth',
                });
                this.$store.state.hubble.startSelectQuote = false;
            }
        },
    },
    methods: {
        ...mapMutations('hubble', ['setQuote', 'toggleSlider', 'setDocumentInfo']),
        async init() {
            await this.getDocumentInfo();
            this.handlePDF();
            this.$refs.documentContent.addEventListener('mouseup', () => {
                const sel = window.getSelection();
                const text = sel.toString();
                this.clearSelectedArea();
                if (text) {
                    this.preContent = text;
                    this.showQuote = true;
                    this.handleSelectedNode();
                }
            });
        },
        handlePDF() {
            PDFJS.getDocument(this.pdfUrl).then((pdf) => {
                this.pageHeights = [];
                for (var i = 1; i <= pdf.numPages; i++) {
                    this.renderPDF(pdf, i);
                }
            });
        },
        getDocumentInfo() {
            return this.$http.get(`/web-api/topic/${this.topicId}/documents`).then((res) => {
                this.setDocumentInfo(res.data);
                this.maxWidth = this.documentInfo.documentPreview?.maxWidth;
                const scale = (this.$refs.documentContent.offsetWidth - 40) / this.maxWidth;
                this.scale = scale < 1 ? 1 : (scale > 2 ? 2 : scale);
            });
        },
        async renderPDF(pdf, num) {
            const page = await pdf.getPage(num);
            const { DPR } = this;
            const viewport = page.getViewport(this.scale * DPR);
            const pageDiv = document.querySelector(`#page-${page.pageIndex + 1}`) || (() => {
                const newPageDiv = document.createElement('div');
                newPageDiv.setAttribute('id', `page-${page.pageIndex + 1}`);
                newPageDiv.setAttribute('class', 'document-page');
                newPageDiv.setAttribute('style', 'position: relative');
                this.$refs.pdfContainer.appendChild(newPageDiv);
                const quoteDiv = document.createElement('div');
                quoteDiv.setAttribute('class', 'hubble-document__selected border-scroll');
                quoteDiv.setAttribute('style', 'display: none');
                newPageDiv.appendChild(quoteDiv);
                const canvas = document.createElement('canvas');
                newPageDiv.appendChild(canvas);
                return newPageDiv;
            })();
            const canvas = pageDiv.querySelector('canvas');
            const context = canvas.getContext('2d');
            canvas.height = viewport.height;
            canvas.width = viewport.width;
            pageDiv.style.height = `${viewport.height / DPR}px`;
            canvas.style.height = `${viewport.height / DPR}px`;
            canvas.style.width = `${viewport.width / DPR}px`;
            this.pageHeights.push(viewport.height / DPR);
            const renderContext = {
                canvasContext: context,
                viewport,
            };
            page.render(renderContext).then(() => {
                this.pdfLoading && (this.pdfLoading = false);
                return page.getTextContent();
            }).then((textContent) => {
                const textLayerDiv = pageDiv.querySelector('.textLayer') || (() => {
                    const newDiv = document.createElement('div');
                    pageDiv.appendChild(newDiv);
                    newDiv.addEventListener('mouseup', () => {
                        this.selectedIndex = num;
                    });
                    return newDiv;
                })();
                const textLayerDivWidth = canvas.style.width;
                const textLayerDivHeight = canvas.style.height;
                textLayerDiv.setAttribute('class', 'textLayer');
                textLayerDiv.setAttribute('style', `width:${textLayerDivWidth};height:${textLayerDivHeight}`);
                if (textLayerDiv) {
                    textLayerDiv.innerHTML = '';
                    PDFJS.renderTextLayer({
                        textContent,
                        container: textLayerDiv,
                        viewport: page.getViewport(this.scale),
                    });
                }
            });
        },
        handleSelectedNode() {
            const sel = window.getSelection();
            // 获取选中元素
            const range = sel.getRangeAt(0);
            const container = range.commonAncestorContainer;
            const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT);
            const selectedNodes = [];
            while (walker.nextNode()) {
                const node = walker.currentNode;
                if (range.intersectsNode(node) && !node.className && node.nodeName !== 'CANVAS') {
                    selectedNodes.push(node);
                }
            }
            // 确定选中元素范围
            let minLeft = null;
            let maxLeft = null;
            let minTop = null;
            let maxTop = null;
            let startNode = null;
            selectedNodes.forEach((node) => {
                const { offsetLeft, offsetWidth, offsetHeight } = node;
                const { top } = node.getBoundingClientRect();
                const realMaxLeft = offsetLeft + offsetWidth;
                const realMaxTop = top + offsetHeight;
                if (!minLeft || offsetLeft < minLeft) {
                    minLeft = offsetLeft;
                }
                if (!maxLeft || realMaxLeft > maxLeft) {
                    maxLeft = realMaxLeft;
                }
                if (!minTop || top < minTop) {
                    minTop = top;
                    startNode = node;
                }
                if (!maxTop || realMaxTop > maxTop) {
                    maxTop = realMaxTop;
                }
            });
            // 给选中范围添加边框
            const parent = startNode.parentNode.parentNode;
            const realTop = this.$refs.documentContent.scrollTop + minTop - parent.offsetTop - 120 - 20;
            const realLeft = minLeft;
            const realWidth = maxLeft - minLeft;
            const realHeight = maxTop - minTop + 4;
            this.currentQuoteArea = parent.querySelector('.hubble-document__selected');
            this.currentQuoteArea.style = `top: ${realTop}px; left: ${realLeft}px; width: ${realWidth}px; height: ${realHeight}px; display: block;`;
            this.quoteTop = `${realTop + maxTop - minTop - 25 + parent.offsetTop}px`;
            this.quoteLeft = `${realLeft + realWidth}px`;
            this.preCoordinate = {
                x: realLeft / parent.offsetWidth,
                y: realTop / parent.offsetHeight,
                width: realWidth / parent.offsetWidth,
                height: realHeight / parent.offsetHeight,
            };
        },
        handleQuote() {
            this.setQuote({
                content: this.preContent,
                documentId: this.documentInfo.documentId,
                quoteCoordinate: this.preCoordinate,
                pageNumber: this.selectedIndex,
                quoteFormat: 'TEXT',
            });
            this.preContent = '';
        },
        clearSelectedArea() {
            this.showQuote = false;
            this.quoteTop = null;
            this.quoteLeft = null;
            this.currentQuoteArea && (this.currentQuoteArea.style.display = 'none');
        },
        handleScroll: throttle(function(e) {
            if (this.scrollDisabled) {
                return;
            }
            let height = 0;
            this.pageHeights.some((el, i) => {
                height += el + 20;
                if (height > e.target.scrollTop) {
                    this.currentPage = i + 1;
                    return true;
                }
            });
        }, 50),
        scrollTo() {
            let top = 0;
            for (let i = 0; i < this.currentPage - 1; i++) {
                top += this.pageHeights[i] + 20;
            }
            top += 10;
            this.scrollDisabled = true;
            this.$refs.documentContent.scrollTo({
                top: top,
                behavior: 'smooth',
            });
            setTimeout(() => {
                this.scrollDisabled = false;
            }, 1000);
        },
        handlePage(type) {
            if (type === 'pre') {
                this.currentPage = this.currentPage - 1 < 1 ? 1 : this.currentPage - 1;
            } else {
                this.currentPage = this.currentPage + 1 > this.documentInfo.pageSize ? this.documentInfo.pageSize : this.currentPage + 1;
            }
            this.scrollTo();
        },
        handleScale(type) {
            this.clearSelectedArea();
            if (type === 'plus') {
                if (this.scale === 2) {
                    return;
                }
                this.scale = this.scale + 0.2 > 2 ? 2 : this.scale + 0.2;
            } else {
                if (this.scale === 0.5) {
                    return;
                }
                this.scale = this.scale - 0.2 < 0.5 ? 0.5 : this.scale - 0.2;
            }
            this.handlePDF();
        },
    },
    mounted() {
        this.init();
    },
};
</script>
<style lang="scss">
.hubble-document{
    display: flex;
    flex-direction: column;
    position: relative;
    &__header{
        height: 55px;
        line-height: 55px;
        padding: 0 20px;
        background: #FFFFFF;
        box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
        position: relative;
        z-index: 9;
    }
    &__title i{
        margin-right: 20px;
        cursor: pointer;
        &.active{
            color: #409EFF;
        }
    }
    &__scale-box{
        position: absolute;
        height: 100%;
        padding: 0 10px;
        top: 0;
        right: 0;
        user-select: none;
        background: #fff;
        i{
            cursor: pointer;
            margin: 0 15px;
            &.el-icon-ssq-jiantou1:nth-child(3){
                transform: rotate(180deg);
            }
        }
        .el-input{
            display: inline-block;
            width: 44px;
            input{
                height: 24px;
                position: relative;
                top: -2px;
                text-align: center;
            }
        }
        span{
            display: inline-block;
            position: relative;
            padding-left: 13px;
            font-size: 18px;
            i{
                position: absolute;
                left: -10px;
                top: -1px;
            }
            em{
                font-weight: normal;
            }
        }
    }
    &__content{
        flex: 1;
        background: #f8f8f8;
        position: relative;
        overflow: auto;
        ::selection {
            background: #0079FE;
        }
        &-loading{
            width: calc(100% - 40px);
            height: 100vh;
            margin: 20px auto;
            background: #fff;
            img{
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
        &-box{
            overflow: auto;
            overflow-x: hidden;
            margin: 20px auto 0;
            background: #f8f8f8;
            position: relative;
            canvas{
                position: absolute;
                top: 0;
                left: 0;
            }
            .textLayer{
                top: 0;
                left: 0;
            }
            .document-page{
                background: #fff;
                margin-bottom: 20px;
            }
        }
    }
    &__quote{
        position: absolute;
        top: var(--quote-top, calc(100% - 70px));
        left: var(--quote-left, calc(100% - 50px));
        z-index: 9;
        right: 20px;
        width: 49px;
        height: 44px;
        background: transparent;
        transition: all .5s;
        background-image: url(~img/quoteIcon.png);
        background-size: cover;
        cursor: pointer;
        &:hover{
            background-image: url(~img/quoteIconHover.png);
        }
        &.fixed{
            top: calc(100% - 56px);
            left: calc(100% - 56px);
        }
    }

    @keyframes blink {
        0% {
            border-color: transparent;
        }
        50% {
            border-color: #0C8AEE;
        }
        100% {
            border-color: transparent;
        }
    }
    .border-scroll {
        position: absolute;
        z-index: 9;
        border: 2px dashed #0C8AEE;
        animation: blink 1s infinite;
    }
}
</style>
