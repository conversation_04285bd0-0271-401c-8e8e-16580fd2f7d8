import Vue from 'vue';

/*
*   风险判断
*   data: {"contractId": 0, "receiverId": 0}
*/
export function doRiskJudgment(contractId, receiverId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/judge-risk`, data);
}

/*
*   获取历史记录
*   data: {"annotationId": 0}
*/
export function queryRiskJudgmentResult(contractId, receiverId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/query-risk-judgment`, data);
}

/*
*   解锁内容
*   data: {"annotationId": 0}
*/
export function unlockRiskJudgmentResult(contractId, receiverId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/unlock-risk-judgment`, data, { noToast: 1 });
}

/*
*   反馈（踩/顶）
*   data: {"annotationId": 0, "feedback": "" }
*   feedback：踩/顶 (LIKE,DISLIKE)
*/
export function feedbackRiskJudgmentResult(contractId, receiverId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/feedback`, data);
}

/*
*   风险判断按钮是否展示
*/
export function buttonDisplay(contractId) {
    return Vue.$http.get(`/contract-api/contract/${contractId}/feature/button-display`);
}

/*
*   hubble工具，风险判断历史记录
*/
export function hubbleRiskHistory(params) {
    return Vue.$http.get(`/web-api/agreement-analysis/analysis-record`, { params });
}

/*
*   hubble工具，风险判断单个记录详情
*/
export function hubbleJudgementDetail(analysisId) {
    return Vue.$http.get(`/web-api/agreement-analysis/${analysisId}/analysis-record-detail`);
}

// /*
// *   hubble工具，获取风险判断余额
// */
// export function overviewRiskDetail() {
//     return Vue.$http.get(`/web-api/users/tool/overview-plan-detail?toolType=协议风险判断`);
// }

/*
*   hubble工具，获取风险判断余额
*/
export function checkSufficient(type) {
    return Vue.$http.post(`/web-api/agreement-analysis/check-if-paid-plan-sufficient?agreementAnalysisType=${type}`);
}

/*
*   hubble工具，获取当前对话内容在原文中的定位
*/
export function viewPosition(analysisId, messageId) {
    return Vue.$http.get(`/web-api/agreement-analysis/${analysisId}/view-position/${messageId}`);
}

/*
*   hubble工具，确定当前操作类型
*/
export function confirmOperateType(analysisId, agreementAnalysisType) {
    return Vue.$http.post(`/web-api/agreement-analysis/${analysisId}/confirm-analysis-type`, {
        agreementAnalysisType,
    });
}

/*
*   初始化获取analysisId
*/
export function initialAnalysis(contractId, receiverId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/init-ai-analysis`, data);
}

/*
*   查询文档记录
*/
export function acquireDocument(contractId, receiverId) {
    return Vue.$http.get(`/contract-api/contract/${contractId}/receiver/${receiverId}/acquire-document-list`);
}

/*
*   查询文档记录
*/
export function getRiskTexConfig(contractId) {
    return Vue.$http.get(`/ents/query-pms-config-data?contractId=${contractId}&featureId=239`);
}

export function getRiskOpen(contractId) {
    return Vue.$http.get(`/ents/query-pms-ent-config-data?contractId=${contractId}&featureId=233`);
}

export function covertToRiskJudgement(contractInterpretationAnalysisId) {
    return Vue.$http.get(`/web-api/agreement-analysis/contract-interpretation/${contractInterpretationAnalysisId}/covert-to-risk-judgement`);
}

/*
*   获取小程序链接
*/
export function fetchAppletUrl(analysisId) {
    return Vue.$http.get(`/web-api/agreement-analysis/${analysisId}/applet-url`);
}
