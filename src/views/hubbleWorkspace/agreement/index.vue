<template>
    <div class="hubble__agreement" v-loading="loading">
        <div class="hubble-workspace__title">{{ $t('agreement.title') }}</div>
        <div class="hubble__agreement-operate">
            <HubbleFilter
                :fieldList="fieldList"
                :agreementIds="agreementIds"
                @change="handleSearch"
                @changeOperate="changeOperate"
                @refresh="getAgreementList"
            ></HubbleFilter>
            <div>
                <el-dropdown @command="handleCommand" placement="bottom-start" trigger="click">
                    <el-button style="margin-right: 6px;width: 210px;">
                        <div class="hubble__agreement-operate__export">
                            <p>{{ $t('agreement.exportList') }}</p>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :disabled="!agreementIds.length" command="allCheck">{{ $t('agreement.exportAllChecked') }}</el-dropdown-item>
                        <el-dropdown-item :disabled="!agreementIds.length" command="currentCheck">{{ $t('agreement.exportCurrentChecked') }}</el-dropdown-item>
                        <el-dropdown-item command="allAccord">{{ $t('agreement.exportAllMatched') }}</el-dropdown-item>
                        <el-dropdown-item command="currentAccord">{{ $t('agreement.exportCurrentMatched') }}</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-button @click="addAgreement">{{ $t('agreement.add') }}</el-button>
                <el-button @click="uploadDialog = true">{{ $t('agreement.upload') }}</el-button>
                <FieldConfig @change="init" :fieldList="fieldList" :workspaceId="workspaceId" :viewId="viewId"></FieldConfig>
            </div>
        </div>
        <div class="hubble-workspace-table">
            <el-table
                :data="agreementData"
                row-class-name="custom-row"
                :border="true"
                style="width: 100%;"
                :max-height="maxTableHeight"
                @selection-change="handleSelectionChange"
            >
                <el-table-column
                    type="selection"
                    width="55"
                >
                </el-table-column>
                <el-table-column
                    v-for="header in curFieldList"
                    :key="header.fieldId"
                    :prop="header.fieldName"
                    :label="header.fieldName"
                    :width="thWidth"
                >
                    <template slot-scope="scope">
                        <el-popover
                            :ref="`popover-${scope.column.id}`"
                            placement="right"
                            width="400"
                            trigger="hover"
                            popper-class="field-popover"
                            v-if="needPopover(scope.row[header.fieldName])"
                        >
                            <PopContent
                                :fieldsReference="scope.row[header.fieldName]"
                                @closePopover="closePopover(scope.column.id)"
                            ></PopContent>
                            <div class="ellipsis-text" slot="reference">
                                {{ scope.row[header.fieldName].fieldValue }}
                            </div>
                        </el-popover>
                        <div v-else>
                            <div class="field-status" v-if="isTaskStatusField(header.fieldName) && scope.row[header.fieldName].fieldValue">
                                <img
                                    class="status-logo"
                                    :class="{
                                        'breath': isDynamic(scope.row[header.fieldName].fieldValue)
                                    }"
                                    :src="iconPath(scope.row[header.fieldName].fieldValue)"
                                    alt="Logo"
                                />
                                <p
                                    class="status-text"
                                    :class="{'dynamic': isDynamic(scope.row[header.fieldName].fieldValue)}"
                                >{{ taskStatus(scope.row[header.fieldName].fieldValue) }}</p>
                            </div>
                            <div v-else>
                                {{ scope.row[header.fieldName].fieldValue }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('agreement.operation')" :fixed="agreementData.length ? 'right' : false" :width="operateWidth">
                    <template slot-scope="scope">
                        <div class="hubble-workspace-table-operate">
                            <span @click="downloadFile(scope.row.agreementId)">{{ $t('agreement.download') }}</span>|
                            <span @click="editAgreement(scope.row.agreementId)">{{ $t('agreement.details') }}</span>|
                            <span @click="deleteAgreement(scope.row.agreementId)">{{ $t('agreement.delete') }}</span>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                layout="prev, pager, next, total"
                :total="total"
                :current-page="pageNumber"
                :page-size="pageSize"
                @current-change="handlePageChange"
            ></el-pagination>
        </div>
        <AgreementDetail
            v-if="showAgreementDetail"
            v-model="showAgreementDetail"
            :agreementId="currentAgreementId"
            :fieldList="fieldList"
            @downloadFile="downloadFile"
            @save="saveDetail"
        ></AgreementDetail>
        <Upload
            v-if="uploadDialog"
            :uploadDialog="uploadDialog"
            :workspaceId="workspaceId"
            :agreementIds="agreementIds"
            :operate="operate"
            @close="checkFiles"
            @changeOperate="changeOperate"
        ></Upload>
    </div>
</template>

<script>
import HubbleFilter from '../components/filter';
import FieldConfig from '../components/fieldConfig';
import AgreementDetail from '../components/agreementDetail';
import { newDownload } from 'utils/download.js';
import Upload from '../components/upload/index.vue';
import PopContent from '../components/popContent/index';
import { mapState } from 'vuex';
export default {
    components: {
        HubbleFilter,
        FieldConfig,
        AgreementDetail,
        Upload,
        PopContent,
    },
    data() {
        return {
            loading: false,
            viewId: '',
            agreementData: [],
            pageNumber: 1,
            pageSize: 50,
            total: 0,
            fieldList: [],
            queryConditionItems: [],
            showAgreementDetail: false,
            currentAgreementId: '',
            uploadDialog: false,
            maxTableHeight: '300px',
            agreementIds: [],
            operate: 'extract',
            popVisible: false,
        };
    },
    computed: {
        ...mapState(['workspaceId']),
        lang() {
            return this.$i18n.locale;
        },
        operateWidth() {
            return {
                zh: '180',
                en: '290',
                ja: '200',
            }[this.lang];
        },
        curFieldList() {
            return this.fieldList.filter(el => el.display);
        },
        thWidth() {
            const tableWidth = document.querySelector('.hubble-workspace-table').offsetWidth - 236;
            const width = tableWidth / this.curFieldList.length;
            return  width < 200 ? 200 : '';
        },
        iconPath() {
            return (val) => {
                switch (val) {
                    case 'EXTRACT_TASK_NOT_STARTED':
                        return require('../img/svg/initial.svg');
                    case 'EXTRACT_TASK_STARTED':
                        return require('../img/svg/search.svg');
                    case 'CONTENT_SEARCH_COMPLETED':
                        return require('../img/svg/format.svg');
                    case 'RESULT_FORMATTING_COMPLETED':
                        return require('../img/svg/proofread.svg');
                    case 'RESULT_VERIFICATION_COMPLETED':
                        return require('../img/svg/finished.svg');
                    default:
                        return '';
                }
            };
        },
        taskStatus() {
            return (val) => {
                switch (val) {
                    case 'EXTRACT_TASK_NOT_STARTED':
                        return this.$t('agreement.taskNotStarted');
                    case 'EXTRACT_TASK_STARTED':
                        return this.$t('agreement.taskStarted');
                    case 'CONTENT_SEARCH_COMPLETED':
                        return this.$t('agreement.contentSearchCompleted');
                    case 'RESULT_FORMATTING_COMPLETED':
                        return this.$t('agreement.resultFormattingCompleted');
                    case 'RESULT_VERIFICATION_COMPLETED':
                        return this.$t('agreement.resultVerificationCompleted');
                    default:
                        return '';
                }
            };
        },
        isDynamic() {
            return (val) => {
                if (val !== 'EXTRACT_TASK_NOT_STARTED' && val !== 'RESULT_VERIFICATION_COMPLETED') {
                    return true;
                } else {
                    return false;
                }
            };
        },
    },
    methods: {
        isTaskStatusField(fieldName) {
            return this.fieldList.find(el => el.fieldName === fieldName).fieldSourceName === 'AGREEMENT_EXTRACT_STATUS';
        },
        saveDetail() {
            this.showAgreementDetail = false;
            this.init();
        },
        downloadFile(agreementId) {
            newDownload(`/web-api/workspace/${this.workspaceId}/download-agreement-file/${agreementId}`);
        },
        addAgreement() {
            this.currentAgreementId = '';
            this.showAgreementDetail = true;
        },
        deleteAgreement(agreementId) {
            this.$confirm(this.$t('agreement.confirmDelete'), this.$t('agreement.prompt')).then(() => {
                this.$http.post(`/web-api/workspace/${this.workspaceId}/remove-agreement-record/${agreementId}`).then(() => {
                    this.init();
                });
            });
        },
        editAgreement(agreementId) {
            this.currentAgreementId = agreementId;
            this.showAgreementDetail = true;
        },
        handleSearch(options) {
            this.queryConditionItems = options;
            this.init();
        },
        getAgreementList() {
            this.loading = true;
            this.$http.post(`/web-api/workspace/${this.workspaceId}/agreements/list-views/view`, {
                pageNumber: this.pageNumber,
                pageSize: 50,
                workspaceId: this.workspaceId,
                queryConditionItems: this.queryConditionItems,
            }).then((res) => {
                this.viewId = res.data.viewId;
                this.total = res.data.pageInfo.totalCount;
                this.agreementData = res.data.fieldList.map((field) => {
                    const result = field.viewFieldItemList.reduce((acc, item) => {
                        item.showValue =
                            item.fieldType === 'BOOLEAN'
                                ? { true: this.$t('agreement.booleanYes'), false: this.$t('agreement.booleanNo') }[
                                    item.fieldValue
                                ]
                                : item.fieldValue;
                        acc[item.fieldName] = {
                            fieldValue: item.showValue,
                            fieldsReferenceList: item.fieldsReferenceList,
                            originalFieldValue: item.originalFieldValue,
                        };
                        return acc;
                    }, {});
                    result.agreementId = field.agreementId;
                    return result;
                });
            }).finally(() => {
                this.loading = false;
            });
        },
        getFieldList() {
            return this.$http(`/web-api/workspace/${this.workspaceId}/agreements/list-views/head-list`).then(res => {
                this.fieldList = res.data;
            });
        },
        async init() {
            await this.getFieldList();
            this.pageNumber = 1;
            this.getAgreementList();
        },
        checkFiles(operate) {
            this.uploadDialog = false;
            this.operate = 'extract';
            if (operate) {
                this.getAgreementList();
            }
        },
        handlePageChange(pageNumber) {
            this.pageNumber = pageNumber;
            this.getAgreementList();
        },
        handleCommand(command) {
            const data = {
                viewId: this.viewId,
                exportType: '',
            };
            switch (command) {
                case 'allCheck':
                    data.exportType = '2';
                    data.agreementIds = this.agreementIds;
                    break;
                case 'currentCheck':
                    data.exportType = '1';
                    data.agreementIds = this.agreementIds;
                    break;
                case 'allAccord':
                    data.exportType = '2';
                    data.queryConditionItems = this.queryConditionItems;
                    break;
                case 'currentAccord':
                    data.exportType = '1';
                    data.queryConditionItems = this.queryConditionItems;
                    break;
                default:
                    break;
            }
            this.downloadFields(data);
        },
        downloadFields(data) {
            this.$http.post(`/web-api/workspace/${this.workspaceId}/agreements/list-views/export`, data, {
                responseType: 'blob',
            }).then((res) => {
                const regex = new RegExp('filename=(.*)');
                const match = res.headers['content-disposition'].match(regex);
                const name = match[1] ? match[1] + '.xlsx' : this.$t('agreement.defaultExportName');
                const u = URL.createObjectURL(res.data);
                const a = document.createElement('a');
                a.download = name;
                a.href = u;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                a.remove();
            });
        },
        handleSelectionChange(val) {
            this.agreementIds = val.map(argee => argee.agreementId);
        },
        changeOperate(type) {
            // if (this.operate === 'extract') {
            //     this.operate = 'restart';
            // } else {
            //     this.operate = 'extract';
            // }
            this.operate = type;
            this.uploadDialog = true;
        },
        needPopover(item) {
            return !!item.fieldsReferenceList && !!item.fieldsReferenceList.length;
        },
        closePopover(columnId) {
            const type = Object.prototype.toString.call(this.$refs[`popover-${columnId}`]);
            if (type === '[object Array]') {
                this.$refs[`popover-${columnId}`].forEach(ele => {
                    ele.doClose();
                });
            } else if (type === '[object Object]') {
                this.$refs[`popover-${columnId}`].doClose();
            }
        },
    },
    created() {
        this.init();
        this.$nextTick(() => {
            this.maxTableHeight = document.querySelector('.hubble-workspace-table').clientHeight - 85;
        });
    },
    mounted() {
    },
};
</script>

<style lang="scss">
.hubble__agreement {
    display: flex;
    flex-direction: column;
    height: 100%;
    &-operate {
        padding: 16px 28px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        .el-button {
            padding: 10px 20px;
            border-radius: 5px;
        }
        &__export {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }
    .el-pagination{
        text-align: right;
        margin-top: 10px;
    }
}
.field-popover {
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 80vh;
    padding-top: 0px;
}
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
@keyframes slide {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
.field-status {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
    .status-logo {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        transition: all 0.3s ease;
        &.breath {
            animation: pulse 1.5s infinite;
        }
    }
    .status-text {
        position: relative;
        overflow: hidden;
    }
    .status-text.dynamic {
        padding-bottom: 4px;
        &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0px;
            width: 100%;
            height: 2px;
            background-color: #4a90e2;
            transform: translateX(-100%);
            animation: slide 2s infinite;
        }
    }
}
</style>
