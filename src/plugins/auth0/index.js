/**
 *  External Modules
 */

import { Auth0Client } from '@auth0/auth0-spa-js';
import { AUTH0_PARAMS } from 'const/const';
// import router from '@/router';
import Vue from 'vue';

function getAuth0Static() {
    const envMap = {
        'localhost:8080': 'local',
        'hubbledoc.tech': 'development',
        'hubbledoc.ai': 'production',
        'hubbledoc.net': 'test',
    };
    return AUTH0_PARAMS[envMap[window.location.host]];
}

/**
 *  Vue.js Plugin Definition
 */
const Auth0 = {
    async install(Vue) {
        const { clientId, domain, redirect_uri, audience } = getAuth0Static();
        const auth0 = new Auth0Client({
            domain,
            clientId,
            authorizationParams: {
                redirect_uri,
                audience,
            },
        });
        Vue.prototype.$auth0 = auth0;
        Vue.$auth0 = auth0;
    },
};

Vue.use(Auth0);
export default Auth0;
