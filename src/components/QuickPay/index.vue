<template>
    <el-dialog
        class="paypal-dialog"
        :title="$t('quickPay.payOnline')"
        :visible.sync="value"
        :close-on-click-modal="false"
        :before-close="handleClose"
        width="400px"
        v-loading.body.fullscreen.lock="payLoading"
    >
        <div class="paypal-dialog__content">
            <div class="paypal-dialog__content-order-info">
                <span>{{ $t('quickPay.openFun') }}</span>
                {{ $t('package.personVersion') }}
            </div>
            <div class="paypal-dialog__content-order-info">
                <span>{{ $t('quickPay.payAmount') }}</span>
                ${{ packageInfo.currentPrice }}
            </div>
            <img class="pay-img" src="~img/paypal.png" alt="">
            <p class="pay-tips"><span>{{ $t('quickPay.poweredBy') }}</span><img src="~img/paypalLogo.png" alt=""></p>
            <el-button :loading="payLoading" class="pay-btn" type="primary" @click="handlePay">{{ $t('quickPay.pay') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import JSConfetti from 'js-confetti';
import io from 'socket.io-client';
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        packageInfo: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            payLoading: false,
        };
    },
    methods: {
        handleClose() {
            this.$emit('input', false);
        },
        handlePay() {
            this.payLoading = true;
            const paymentWindow = window.open();
            this.$http.post('/web-api/payment', {
                planType: this.packageInfo.planType,
            }).then(res => {
                paymentWindow.location = res.data.paymentUrl;
            }).catch(() => {
                paymentWindow.close();
                this.payLoading = false;
            });
        },
        // 支付成功
        handlePaySuccess() {
            this.$emit('handlePaySuccess');
            const confetti = new JSConfetti();
            confetti.addConfetti().then(() => {
                this.$MessageToast.success(this.$t('tips.openSuccess'));
            });
        },
        initSocket() {
            this.socket = io('/web-hubble/ask-pdf', {
                path: '/web-hubble',
                reconnectionDelay: 5000,
                query: {
                    topicId: this.topicId,
                },
                transports: ['polling', 'websocket'],
                extraHeaders: {
                    Authorization: 'bearer ' + this.$cookie.get('access_token'),
                },
            });
            // 监听连接成功事件
            this.socket.on('connect', () => {
                console.log('连接成功');
            });
            this.socket.on('connect_error', (error) => {
                console.log('连接失败', error);
            });
            this.socket.on('payment-event', (data) => {
                console.log('支付结果', data);
                try {
                    if (data) {
                        this.handlePaySuccess();
                    } else {
                        this.$MessageToast(this.$t('tips.openFailed'));
                    }
                    this.payLoading = false;
                } catch (error) {
                    console.log('支付结果解析失败', error);
                }
            });
        },
    },
    created() {
        this.initSocket();
    },
};
</script>

<style lang="scss">
.paypal-dialog{
    font-size: 14px;
    &__content{
        padding: 20px 30px 40px;
        &-order-info{
            line-height: 40px;
            span{
                color: #000;
                display: inline-block;
                width: 150px;
                text-align: right;
                margin-right: 20px;
            }
        }
        .pay-img{
            width: 100%;
            margin: 20px 0 10px;
        }
        .pay-tips{
            height: 20px;
            line-height: 20px;
            color: #999;
            span{
                float: left;
            }
            img{
                width: 48px;
                margin: 5px 0 0 5px;
            }
        }
        .pay-btn{
            width: 100%;
            margin-top: 20px;
        }
    }
    .el-dialog__body{
        padding: 0;
    }
}
</style>
