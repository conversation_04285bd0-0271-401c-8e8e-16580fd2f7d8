<template>
    <el-dialog
        class="hubble-contract-package"
        :visible="show"
        :modal-append-to-body="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
        <div slot="title" class="hubble-contract-package_header">
            <span class="title">{{ $t('contractCompare.myPackage') }}</span>
            <i class="el-icon-close" @click="handleClose"></i>
        </div>
        <div class="package-content">
            <div class="package-content-left">
                <div><i class="el-icon-ssq-taocanxiangqing"></i>{{ $t('contractCompare.packageDetail') }}</div>
            </div>
            <div class="package-content-right">
                <div class="total" v-if="list.length">
                    <span class="desc">{{ $t('hubblePackage.remaining') }}：</span>
                    <p class="content">
                        <span class="pack" v-for="(pack, index) in packageList" :key="pack.title">
                            {{ getPackTitle(pack.title) }}: {{ pack.total }} {{ productConfig.unitPer }} <span v-if="index !== packageList.length - 1">;</span>
                        </span>
                    </p>
                </div>
                <div class="package-content-right-wrap">
                    <div v-for="item in list" :key="item.toolPlanId">
                        <div class="package-item">
                            <p class="package-name">{{ item.toolProductName }}</p>
                            <div class="bar">
                                <div class="progress" :style="{ right: getProgress(item) }"></div>
                            </div>
                            <div class="info">
                                <span>{{ $t('hubblePackage.usedPages') + ' ' + (item.productOfferingCount - item.productBalanceCount) + ' ' + productConfig.unitPer }}</span>
                                {{ $t('hubblePackage.remaining') + ' ' + item.productBalanceCount + ' ' + productConfig.unitPer }} / {{ $t('hubblePackage.total') + ' ' + item.productOfferingCount + ' ' + productConfig.unitPer }}
                            </div>
                        </div>
                        <div class="package-time">{{ $t('entAuth.validity') }}：{{ moment(item.expireTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                    </div>
                    <NoData v-if="!list.length" />
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import NoData from '../noData';
// import { getPackageDetail } from 'src/api/comparison.js';
import moment from 'dayjs';

export default {
    components: {
        NoData,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        toolType: {
            type: [String, Array],
            default: () => '比对', // 比对 翻译
        },
        productConfig: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            list: [],
            packageList: [],
        };
    },
    computed: {
        total() {
            let total = 0;
            this.list.forEach((item) => total += item.productBalanceCount);
            return total;
        },
        getPackTitle() {
            return (val) => {
                if (val === '协议风险判断') {
                    return this.$t('agent.riskTitle');
                } else if (val === '协议要素提取') {
                    return this.$t('agent.extractTitle');
                } else if (val === '协议风险判断(深度推理)') {
                    return this.$t('judgeRisk.deepInference');
                } else {
                    return val;
                }
            };
        },
    },
    watch: {
        show(val) {
            if (val) {
                this.packageList = [];
                const type = Object.prototype.toString.call(this.toolType);
                if (type === '[object String]') {
                    this.getPackageDetail(this.toolType);
                } else if (type === '[object Array]') {
                    this.toolType.forEach(tool => {
                        this.getPackageDetail(tool);
                    });
                }
            } else {
                this.list = [];
            }
        },
    },
    methods: {
        moment,
        getProgress({ productOfferingCount, productBalanceCount }) {
            return `${(productBalanceCount / productOfferingCount) * 100}%`;
        },
        handleClose() {
            this.$emit('handleClose');
        },
        getPackageDetail(toolType) {
            // 模拟获取套餐详情
            const mockData = [
                {
                    toolPlanId: 1,
                    toolProductName: '基础套餐',
                    productOfferingCount: 10,
                    productBalanceCount: 5,
                    expireTime: '2024-12-31 23:59:59',
                    toolType: toolType,
                },
                {
                    toolPlanId: 2,
                    toolProductName: '专业套餐',
                    productOfferingCount: 50,
                    productBalanceCount: 30,
                    expireTime: '2024-12-31 23:59:59',
                    toolType: toolType,
                },
            ];

            this.list = this.list.concat(mockData);
            mockData.forEach(pack => {
                if (!this.packageList.length) {
                    this.packageList.push({ title: toolType, total: pack.productBalanceCount });
                } else {
                    const index = this.packageList.findIndex(ele => ele.title === pack.toolType);
                    if (index === -1) {
                        this.packageList.push({ title: toolType, total: pack.productBalanceCount });
                    } else {
                        this.packageList[index].total += pack.productBalanceCount;
                    }
                }
            });
        },
    },
};
</script>

<style lang="scss">
    $--color-text-primary: #333333;
    $--color-text-second: #999999;
    $--color-border: #EEEEEE;
    $--color-bg: #F8F8F8;
    $--color-white: #FFFFFF;

    .hubble-contract-package {
        z-index: 99998 !important;
        *{box-sizing: border-box;}
        .el-dialog--small {
            width: 600px;
        }
        .el-dialog__header {
            padding: 0;
            border-bottom: 1px solid $--color-border;
            .hubble-contract-package_header {
                height: 50px;
                padding: 0 20px 0 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .title {
                    font-size: 16px;
                    color: $--color-text-primary;
                }
                .el-icon-close {
                    cursor: pointer;
                    font-size: 12px;
                    color: $--color-text-second;
                }
            }
            .el-dialog__headerbtn {
                display: none;
            }
        }
        .el-dialog__body {
            padding: 0px;
            .package-content {
                display: flex;
                &-left {
                    width: 150px;
                    padding: 10px 10px 0;
                    background-color: $--color-bg;
                    div {
                        width: 129px;
                        height: 40px;
                        font-size: 14px;
                        background-color: $--color-white;
                        line-height: 40px;
                        padding-left: 20px;
                        i {
                            margin-right: 10px;
                        }
                    }
                }
                &-right {
                    flex: 1;
                    .total {
                        padding-left: 30px;
                        margin-top: 5px;
                        line-height: 30px;
                        width: 100%;
                        display: flex;
                        .desc {
                            flex-shrink: 0;
                        }
                        .content {
                            flex-grow: 1;
                            flex-wrap: wrap;
                            display: flex;
                            white-space: pre;
                            .pack {
                                display: inline-block;
                                font-size: 12px;
                                line-height: 29px;
                                color: #999;
                            }
                        }
                    }
                    &-wrap {
                        height: 370px;
                        overflow: auto;
                        .package-time {
                            width: 390px;
                            margin: auto;
                            margin-top: 12px;
                            font-size: 12px;
                            color: $--color-text-second;
                            text-align: right;
                        }
                        .package-item {
                            width: 90%;
                            border-radius: 4px;
                            height: 75px;
                            padding: 10px 25px 0;
                            margin: 20px auto 0;
                            background-color: $--color-bg;
                            .package-name {
                                margin-bottom: 8px;
                                font-size: 12px;
                                color: $--color-text-primary;
                            }
                            .bar {
                                position: relative;
                                height: 6px;
                                border-radius: 6px;
                                background-color: $--color-white;
                                .progress {
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    bottom: 0;
                                    border-radius: 6px;
                                    background-image: linear-gradient(90deg, #76C2FF 0%, #0C8AEE 100%);
                                }
                            }
                            .info {
                                display: flex;
                                justify-content: space-between;
                                margin-top: 12px;
                                font-size: 12px;
                                color: $--color-text-second;
                                span {
                                    color: $--color-text-primary;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
</style>
