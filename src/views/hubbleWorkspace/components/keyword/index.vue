<template>
    <div class="keyword-introduce">
        <div class="extract-keyword">
            <!-- <div class="extract-keyword-title">
                {{ keyword }}
                <span @click="addKeyword">
                    <i class="el-icon-ssq-jia"></i>
                    {{ $t('contractCompare.addKeyword', { keyword }) }}
                </span>
            </div> -->
            <div class="extract-keyword-content" v-loading="loading">
                <el-table
                    ref="multipleTable"
                    :data="keywords"
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column
                        type="selection"
                        width="40"
                    ></el-table-column>
                    <el-table-column
                        :label="$t('workspace.agreement.terms')"
                    >
                        <template slot-scope="{ row }">
                            <div @mouseenter="mouseenter(row)">{{ row.extractExpectedFieldName }}</div>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- <el-checkbox-group v-model="checkList" @change="changeField">
                    <el-checkbox
                        class="checkbox-edit"
                        v-for="value in keywords"
                        :key="value.extractExpectedFieldId"
                        :label="value.extractExpectedFieldId"
                        :class="{ active: activeField.extractExpectedFieldId === value.extractExpectedFieldId }"
                        @mouseenter.native="mouseenter(value)"
                    >
                        {{ value.extractExpectedFieldName }}
                    </el-checkbox>
                    <div class="no-extract-info" v-if="!keywords.length">
                        <NoData></NoData>
                    </div>
                </el-checkbox-group> -->
            </div>
        </div>
        <div class="extract-introduce">
            <div class="extract-introduce-title">
                {{ $t('workspace.introduce', { keyword }) }}
            </div>
            <div class="extract-introduce-content">
                <p style="padding: 15px 15px 0;">{{ $t('workspace.termsDetail') }}</p>
                <el-input
                    class="introduce-input"
                    disabled
                    resize="none"
                    type="textarea"
                    v-model="introduce"
                    :placeholder="$t('workspace.optional')"
                />
                <p style="padding: 0 15px;">{{ $t('workspace.extractFormat') }}</p>
                <el-input
                    class="introduce-input"
                    disabled
                    resize="none"
                    type="textarea"
                    v-model="extractFormatRequirement"
                    :placeholder="$t('workspace.optional')"
                />
            </div>
        </div>
    </div>
</template>

<script>
import { addExtractField } from 'src/api/extract.js';
// import NoData from 'components/noData';
export default {
    // components: { NoData },
    props: {
        keyword: {
            type: String,
            default: '',
        },
        workspaceId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            keywords: [],
            checkList: [],
            introduce: '',
            extractFormatRequirement: '',
            extractType: {
                TEXT: this.$t('termManagement.extractType.text'),
                LONG_TEXT: this.$t('termManagement.extractType.longText'),
                DATE: this.$t('termManagement.extractType.date'),
                NUMBER: this.$t('termManagement.extractType.number'),
                BOOLEAN: this.$t('termManagement.extractType.boolean'),
            },
            adding: false,
            loading: false,
            activeField: {},
        };
    },
    methods: {
        initFileConfig(focus) {
            this.$http(`/web-api/workspace/${this.workspaceId}/extract-expected-field/acquire`).then(res => {
                this.keywords = res.data.filter(el => {
                    el.showType = this.extractType[el.extractContentFieldType];
                    if (el.fieldSource !== 'SYSTEM_DEFAULT') {
                        return true;
                    }
                });
                // console.log(this.keywords);
                if (focus) {
                    this.activeField = this.keywords[this.keywords.length - 1];
                    this.introduce = this.activeField.extractExpectedFieldDefinition;
                    this.extractFormatRequirement = this.activeField.extractFormatRequirement;
                    this.$nextTick(() => {
                        const input = document.querySelector('.extract-keyword-content .el-checkbox-group .edit-input input');
                        input.scrollIntoView();
                        input.focus();
                    });
                }
            });
        },
        addKeyword() {
            if (this.adding) {
                return;
            }
            this.adding = true;
            addExtractField({
                extractExpectedFieldName: '',
                extractExpectedFieldDefinition: '',
            }).then(() => {
                this.initFileConfig(true);
            })
                .finally(() => {
                    this.adding = false;
                });
        },
        handleSelectionChange(value) {
            const fields = value.map(field => field.extractExpectedFieldId);
            this.$emit('changeField', fields);
        },
        mouseenter(value) {
            this.activeField = value;
            this.introduce = this.activeField.extractExpectedFieldDefinition;
            this.extractFormatRequirement = this.activeField.extractFormatRequirement;
        },
        reset() {
            this.checkList = [];
        },
    },
    mounted() {
        this.initFileConfig();
    },
};
</script>

<style lang="scss" scoped>
.keyword-introduce {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    justify-content: space-between;
    background-color: #FFFFFF;
    .extract-keyword {
        width: calc(45% - 5px);
    }
    .extract-introduce {
        width: calc(55% - 5px);
    }
}
.extract-keyword, .extract-introduce {
    height: 100%;
    border-radius: 4px;
    border: 1px solid #EEEEEE;
    &-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44px;
        padding: 0 10px;
        font-size: 14px;
        color: #3D3D3D;
        background-color: #F8F8F8;
        span {
            cursor: pointer;
            color: $theme-color;
        }
    }
    &-content {
        overflow-y: auto;
        position: relative;
        height: calc(100% - 44px);
    }
    .introduce-input {
        height: calc(48% - 20px);
        outline: none;
        padding: 15px;
        line-height: 20px;
        color: #333333;
        box-sizing: border-box;
        textarea {
            height: 100%;
        }
        &.el-textarea.is-disabled .el-textarea__inner {
            color: #333333;
        }
    }
}
.el-checkbox.checkbox-edit {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding: 0;
    padding-left: 10px;
    margin-left: 0;
    display: flex;
    align-items: center;
    cursor: pointer;
    &.active {
        background-color: #F4FAFF;
    }
    .text {
        width: calc(100% - 30px);
        display: inline-block;
        margin-left: 10px;
        margin-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    i {
        color: $theme-color;
        &:first-of-type {
            margin-right: 8px;
        }
    }
    .el-checkbox__label {
        display: flex;
        align-items: center;
        width: calc(100% - 21px);
    }
    .el-input {
        width: calc(100% - 20px);
        height: 28px;
        input {
            height: 28px;
            border: none;
            box-shadow: none;
            vertical-align: super;
        }
    }
    .input-wrap {
        display: inline-block;
        position: relative;
        height: 32px;
        width: 100%;
        border-radius: 1px;
        border: 1px solid #ccc;
        background-color: #FFFFFF;
        .save {
            width: 14px;
            height: 14px;
            position: absolute;
            top: 8px;
            font-size: 14px;
            border-radius: 2px;
            text-align: center;
            background-color: #EEEEEE;
            i {
                vertical-align: top;
                font-size: 12px;
                color: #999999;
            }
        }
    }
}
.no-extract-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
