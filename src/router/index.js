import Vue from 'vue';
import VueRouter from 'vue-router';

import {
    routerOnErrorFun,
    routerPushFun,
    routerReplaceFun,
    publicBeforeEachHooks,
} from './utilityFunctions.js';
import { loadLanguageAsync } from 'lang';
import Index from 'views/index.vue';

Vue.use(VueRouter);

const NotFound =
    () =>
        import(/* webpackChunkName: "error" */ 'views/error/NotFound.vue');
const Forbidden =
    () =>
        import(/* webpackChunkName: "error" */ 'views/error/Forbidden.vue');

const routes = [
    { path: '*', component: NotFound },
    {
        path: '/',
        component: Index,
        children: [{
            path: '',
            redirect: 'landing',
            meta: { noLogin: true },
        }, {
            path: 'landing',
            component: () => import(/* webpackChunkName: "landing" */ 'views/landing/index.vue'),
            meta: {
                noLogin: true,
                title: 'Hubble Landing',
            },
        }, {
            path: 'hubble-workspace',
            redirect: 'hubble-workspace/agreement',
            component: () => import(/* webpackChunkName: "hubble" */ 'views/hubbleWorkspace/index.vue'),
            children: [{
                path: 'agreement',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/hubbleWorkspace/agreement/index.vue'),
            }, {
                path: 'review',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/hubbleWorkspace/review/index.vue'),
            }, {
                path: 'term',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/hubbleWorkspace/term/index.vue'),
            },
            {
                path: 'review-details',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/hubbleWorkspace/review/detail/index.vue'),
            },
            {
                path: 'review-manage',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/hubbleWorkspace/review/manage/index.vue'),
            }],
        },
        {
            path: 'contract-risk-judgement',
            component: () => import(/* webpackChunkName: "contract-risk" */ 'views/contractRiskJudgement/index.vue'),
            meta: {
                title: 'Contract Risk Judgement',
            },
        },
        {
            path: 'transition',
            component: () => import(/* webpackChunkName: "error" */ 'views/transition/index.vue'),
            meta: {
                noLogin: true,
            },
        },
        {
            path: 'notFound',
            component: NotFound,
            meta: {
                noLogin: true,
            },
        },
        {
            path: 'forbidden',
            component: Forbidden,
            meta: {
                noLogin: true,
            },
        },
        // {
        //     path: 'hubble',
        //     component: () => import(/* webpackChunkName: "hubble" */ 'views/index.vue'),
        //     children: [{
        //         path: 'chat/:topicId',
        //         component: () => import(/* webpackChunkName: "hubble" */ 'views/chat/index.vue'),
        //         meta: {
        //             hasSlider: true,
        //             isHubblePage: true,
        //         },
        //     }, {
        //         path: 'shareChat/:topicId',
        //         component: () => import(/* webpackChunkName: "hubble" */ 'views/chat/index.vue'),
        //         meta: {
        //             isSharePage: true,
        //             isHubblePage: true,
        //         },
        //     }, {
        //         path: 'upload',
        //         component: () => import(/* webpackChunkName: "hubble" */ 'views/upload/index.vue'),
        //         meta: {
        //             isUploadPage: true,
        //             hasSlider: true,
        //             isHubblePage: true,
        //         },
        //     }, {
        //         path: 'share/:token',
        //         component: () => import(/* webpackChunkName: "hubble" */ 'views/share/index.vue'),
        //         meta: {
        //             isSharePage: true,
        //             noLogin: true,
        //         },
        //     }, {
        //         path: 'paymentResult',
        //         component: () => import(/* webpackChunkName: "hubble" */ 'views/paymentResult/index.vue'),
        //         meta: {
        //             noLogin: true,
        //         },
        //     }],
        // },
        ],
    },
];

Vue.use(VueRouter);

const router = new VueRouter({
    name: 'hubble',
    mode: 'history',
    base: 'web',
    routes: [...routes],
    scrollBehavior() {
        return {
            x: 0,
            y: 0,
        };
    },
});

/* 401登陆跳转依赖此方法，每个模块都一定要引用 start */
VueRouter.prototype.push = routerPushFun;
VueRouter.prototype.replace = routerReplaceFun;
/* 401登陆跳转依赖此方法，每个模块都一定要引用 end */

// 切换导航栏偶尔报错'Loading chunk *** failed'
router.onError(routerOnErrorFun);

router.beforeEach((to, from, next) => loadLanguageAsync().then(() => next()));

router.beforeEach(async(to, from, next) => {
    // 进入第一个页面路由前remove loading
    const $indexLoading = document.querySelector('#index-loading');
    try {
        $indexLoading && $indexLoading.remove();
    } catch (err) {
        $indexLoading.parentNode.removeChild($indexLoading);
    }
    next();
});

router.beforeEach(publicBeforeEachHooks);

export default router;
