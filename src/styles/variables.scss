
@charset "UTF-8";
/**
 * @desc 通用变量，先引入 elementUI 的变量，会做一些简单的转换，兼容原有的变量
 * <AUTHOR>
 * @date 2020-03-31
 */

@import './base_127fd2.scss';

// elementUI 的变量在日常使用可能比较长，可以在这里用较短的变量代替
// 这里暂时转换了主题色和主题色的hover色，后续可以补充，但最好还是使用一个标准
$theme-color: $--color-primary;
$theme-color-light: $--color-primary-light-1;

/* 以下是原有的一些变量
---------------------------------------------------------------- */
// 全局变量设置

$base-width: 1280px;
// 网页主色
$base-color: $theme-color; // 127FD2
// 文字颜色
$text-color-light: #dddddd;
$text-color-lighter: #eeeeee;
// 通用线框颜色
$border-color: $--border-color-light; // ddd
$border-radius: 1px;
// 背景颜色
$background-color: $theme-color;
$background-color-gray: #999999;
$background-color-dark: #eeeeee;
// 导航头主色
$header-color: #002b45;
$header-color-light: #054068;
// 头部高度
// $uc-header-height: 63px;
$header-height: 63px;
// 通用通过型绿色
$right-color: $--color-success;
// 按钮
$btn-bgcolor: $theme-color;
$btn-hover-bgcolor: $theme-color;
$btn-disabled-font-color: #CCCCCC;
