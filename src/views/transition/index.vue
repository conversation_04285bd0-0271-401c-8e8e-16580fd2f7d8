<template>
    <div class="hubble-page__transition"></div>
</template>

<script>

export default {
    name: 'Transition',
    methods: {
        onRedirectCallback(appState = {}) {
            console.log('onRedirectCallback = ', appState && appState.targetPath
                ? appState.targetPath
                : window.location.pathname);
            let targetUrl = appState.targetPath;
            if (appState.targetPath.includes('/web')) { // 环境上跳转会携带/web，/web会重复
                targetUrl = targetUrl.split('/web')[1];
            }
            const path = targetUrl !== '/transition' ? targetUrl : '/hubble-workspace/agreement';
            this.$router.replace(path);
        },
    },
    async created() {
        const { state, code, error } = this.$route.query;
        if (!(code || error) || !state) { // 非登录跳转不需要处理逻辑
            return this.$router.replace('/hubble-workspace/agreement');
        }
        const { appState } = await this.$auth0.handleRedirectCallback();
        const isAuthenticated = await this.$auth0.isAuthenticated();
        if (isAuthenticated) {
            const token = await this.$auth0.getTokenSilently();
            this.$cookie.set('access_token', token);
            this.onRedirectCallback(appState);
        }
    },
};
</script>

