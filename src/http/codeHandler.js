// HTTP 请求状态码为 409 的情况下后端返回的 code 字段
import Vue from 'vue';
import i18n from 'src/lang/';

// 通用异常提示code白名单，具体异常在业务层处理
const NOTOAST_CODE_LIST = [
    'U-180012', // 混合云上传的文件已有签名，暂不支持此类文件
    '180012', // 上传的文件已有签名，暂不支持此类文件，具体的异常提示通过弹窗展示
    '020510', // 认领鉴权报错不报错，直接跳转新页面
];
/**
 *
 * @param {*} code HTTP 状态码
 * @param {*} resData 接口返回的数据
 * @param {*} defaultMessage 通用的消息提示
 * @param {*} error 异常消息体
 * @param {*} status 状态码
 */
export function codeHandler(code, resData, defaultMessage, error, status) {
    let message = defaultMessage;
    if (status >= 500) {
        message = message || i18n.t('tips.systemOcupied');
    } else if (error.message === 'Network Error') {
        message = i18n.t('tips.networkError'); // 当前网络不可用，请检查你的网络设置
    }
    if (!NOTOAST_CODE_LIST.includes(code)) { // 过滤不需要异常处理的code list
        message && Vue.$MessageToast.error(message);
    }
}
