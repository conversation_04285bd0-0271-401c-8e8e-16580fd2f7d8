<template>
    <div class="mgapprovenote mgapprovenote__box" ref="mgapprovenote" :class="`mgapprovenote-${noteData.noteId}`" :style="boxStyle">
        <i class="el-icon-ssq-guanbi1 mgapprovenote__box-close" @click="deleteItem" v-if="!noteData.history"></i>
        <div class="mgapprovenote__tabs">
            <div class="mgapprovenote__tabs-item" @click="distributeReview" v-if="!noteData.distributionIds">
                <el-button type="primary">{{ $t('workspace.review.distribution') }}</el-button>
            </div>
        </div>
        <div class="mgapprovenote__move" v-if="!noteData.distributionIds" @mousedown="handleDrag">
            <div class="mgapprovenote__move-top"></div>
            <div class="mgapprovenote__move-right"></div>
            <div class="mgapprovenote__move-bottom"></div>
            <div class="mgapprovenote__move-left"></div>
            <div class="mgapprovenote__move-all"></div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        noteData: {
            type: Object,
            default: () => {},
        },
        reviewVersionFileId: {
            type: String,
            default: '',
        },
        workspaceId: {
            type: String,
            default: '',
        },
        isApprovenoteMove: {
            type: Boolean,
            default: false,
        },
        page: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            currentRect: null,
        };
    },
    computed: {
        boxStyle() {
            const { x, y, width, height } = this.noteData.contentPosition;
            return {
                top: `${y * 100}%`,
                left: `${x * 100}%`,
                width: `${width * 100}%`,
                height: `${height * 100}%`,
            };
        },
    },
    watch: {

    },
    methods: {
        distributeReview() {
            this.$emit('showDistribute', this.noteData);
        },
        deleteItem() {
            this.$emit('deleteNote', this.noteData.noteId);
        },
        handleDrag(e) {
            const domList = document.getElementsByClassName('contract-page-content');
            this.currentRect = domList[this.page].getBoundingClientRect();
            this.startDrag(e);
        },
        startDrag(event) {
            this.$emit('setApprovenoteMove', true);
            const realTop = document.getElementById('docContainer').scrollTop - this.currentRect.top;
            const moveType = event.target.className.split('-')[1];
            const { x, y, width, height } = this.noteData.contentPosition;
            const startPos = { x, y };
            const endPos = { x, y };
            switch (moveType) {
                case 'top':
                    startPos.y = y + height;
                    break;
                case 'left':
                    startPos.x = x + width;
                    break;
                case 'all':
                    startPos.x = event.clientX;
                    startPos.y = event.clientY;
                    break;
            }
            const handleMove = (moveEvent) => {
                if (!this.isApprovenoteMove) {
                    return;
                }
                switch (moveType) {
                    case 'all':
                        this.noteData.contentPosition = {
                            ...this.noteData.contentPosition,
                            x: x + (moveEvent.clientX - event.clientX) / this.currentRect.width,
                            y: y + (moveEvent.clientY - event.clientY) / this.currentRect.height,
                        };
                        break;
                    case 'top':
                    case 'bottom':
                        endPos.y = (moveEvent.clientY + realTop) / this.currentRect.height;
                        this.noteData.contentPosition = {
                            ...this.noteData.contentPosition,
                            y: Math.min(startPos.y, endPos.y),
                            height: Math.abs(startPos.y - endPos.y),
                        };
                        break;
                    case 'left':
                    case 'right':
                        endPos.x = (moveEvent.clientX - this.currentRect.left) / this.currentRect.width;
                        this.noteData.contentPosition = {
                            ...this.noteData.contentPosition,
                            x: Math.min(startPos.x, endPos.x),
                            width: Math.abs(startPos.x - endPos.x),
                        };
                        break;
                }
            };
            const handleUp = () => {
                this.getReviewContent(this.noteData.reviewVersionFileId, this.noteData.contentPosition).then(res => {
                    this.noteData.content = res;
                });
                this.$emit('setApprovenoteMove', false);
                document.removeEventListener('mousemove', handleMove);
                document.removeEventListener('mouseup', handleUp);
            };
            document.addEventListener('mousemove', handleMove);
            document.addEventListener('mouseup', handleUp);
        },
        getReviewContent(reviewVersionFileId, data) {
            return new Promise((resolve) => {
                this.$http.post(`/web-api/workspace/${this.workspaceId}/agreement-review/content/${reviewVersionFileId}`, data).then(res => {
                    resolve(res.data.content);
                });
            });
        },
    },
    created() {
        // setTimeout(() => {
        //     this.noteData.annotationResultContent && (this.popoverVisible = true);
        //     document.querySelector(`.mgapprovenote-${this.noteData.annotationId}`).scrollIntoView({ behavior: 'smooth', block: 'center' });
        // }, 100);
    },
};
</script>

<style lang="scss">
.mgapprovenote{
    font-size: 14px;
    z-index: 99;
    &__box{
        position: absolute;
        border: 1px dashed $theme-color;
        border-radius: 3px;
        background: rgba($color: #E7F3FB, $alpha: .1);
        cursor: pointer;
        &-close{
            cursor: pointer;
            position: absolute;
            top: -6px;
            right: -6px;
            background: #fff;
            z-index: 9;
        }
        &.disabled{
            cursor: default;
        }
    }
    &__tabs{
        font-size: 12px;
        position: absolute;
        top: -45px;
        right: 0;
        border-radius: 5px;
        opacity: 1;
        background: #FFFFFF;
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: row;
        align-items: center;
        .line{
            height: 30px;
            border-right: 1px solid #D8D8D8;
        }
        &-item{
            cursor: pointer;
            i{
                padding: 10px 24px;
            }
            &:hover{
                color: $theme-color;
            }
        }
        &-SAQ{
            position: absolute;
            top: -40px;
            right: 0;
            padding: 7px;
            border-radius: 5px;
            background: #f4f4f4;
            display: flex;
            flex-direction: row;
            cursor: pointer;
            align-items: center;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
            border: 1px solid #ddd;
        }
    }
    &__operate{
        color: #fff;
        line-height: 28px;
        background: $theme-color;
        position: absolute;
        top: -30px;
        right: 0;
        display: flex;
        flex-direction: row;
        &-item{
            padding: 0 8px;
            display: flex;
            cursor: pointer;
            span{
                white-space: nowrap;
            }
            i{
                display: block;
                line-height: 28px;
                margin-right: 5px;
            }
            & + span{
                border-left: 1px solid #fff;
            }
        }
    }
    &__popover{
        width: unset !important;
        pre{
            padding: 20px 0;
            white-space: pre-wrap;
            max-height: 40vh;
            overflow: auto;
        }
        p{
            font-size: 12px;
            color: #ccc;
            text-align: right;
        }
    }
    &__content{
        width: 100%;
        height: 100%;
        position: absolute;
    }
    &__move{
        div{
            position: absolute;
            // background: red;
        }
        &-top, &-bottom{
            height: 2px;
            right: 0;
            left: 0;
            cursor: row-resize;
        }
        &-right, &-left{
            width: 2px;
            top: 2px;
            bottom: 2px;
            cursor: col-resize;
        }
        &-top{
            top: 0;
        }
        &-bottom{
            bottom: 0;
        }
        &-right{
            right: 0;
        }
        &-left{
            left: 0;
        }
        &-all{
            top: 2px;
            right: 2px;
            bottom: 2px;
            left: 2px;
            cursor: move;
        }
    }
}
</style>
