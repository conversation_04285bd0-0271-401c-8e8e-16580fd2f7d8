<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit" />
    <meta name="msapplication-tap-highlight" content="no">
    <!-- <meta http-equiv="Cache-Control" content="no-siteapp" /> -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta content="Hubble,ChatGPT,,UDP,IDP,unstructured data,pdf,doc,document,file,ai,artificial intelligence,business automation,automation"
          name="keywords">
    <meta id="html-desc"
          content="Using ChatGPT as its backbone, <PERSON><PERSON> stands out as a proficient document assistant, adept at swiftly locating, extracting, and condensing crucial details. This ensures tasks are completed with finesse, and it also paves the way for enhanced collaboration across diverse sectors."
          name="description">
    <title id="html-title">Hubble - communicate with the document!</title>
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico">
    <script id="head-script-slot" async="true"></script>
<!--    <script src="https://g.alicdn.com/AWSC/AWSC/awsc.js"></script>-->
</head>

<body>
<script>
    let setScrollTop = function () {
        let top = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop
        window.scrollTo(0, top + 1)
        setTimeout(function() {
            window.scrollTo(0, top)
        }, 300)
    }
    document.addEventListener('focusout', setScrollTop, false);
</script>
<div id="index-loading" style="height: 100%;text-align:center;">
    <style>
        .circular{
            top: 50%;
            left: 50%;
            margin: -20px 0 0 -20px;
            position: absolute;
            height: 40px;
            width: 40px;
            animation: loading-rotate 2s linear infinite
        }
        .circular .path{
            animation: loading-dash 1.5s ease-in-out infinite;
            stroke-dasharray: 90,150;
            stroke-dashoffset: 0;
            stroke-width: 2;
            stroke: #409eff;
            stroke-linecap: round;
        }
        @keyframes loading-rotate {
            to {
                transform: rotate(1turn)
            }
        }
        @keyframes loading-dash {
            0% {
                stroke-dasharray: 1,200;
                stroke-dashoffset: 0
            }

            50% {
                stroke-dasharray: 90,150;
                stroke-dashoffset: -40px
            }

            to {
                stroke-dasharray: 90,150;
                stroke-dashoffset: -120px
            }
        }
    </style>
    <svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg>
</div>
<div id="app"></div>
</body>

</html>
