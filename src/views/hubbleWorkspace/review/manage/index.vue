<template>
    <div class="hubble-workspace">
        <div class="hubble-review-header">
            <div class="hubble-review-header__title">
                <p>{{ $t('workspace.review.manageReview') }}</p>
            </div>
            <div class="hubble-review-header__operate">
                <div class="hubble-review-header__operate-body">
                    <div class="hubble-review-header__operate-body-text">
                        <span class="hubble-review-header__operate-body-text-doc">{{ reviewName }}</span>
                        <span class="hubble-review-header__operate-body-text-code">{{ $t('workspace.review.reviewDesc', {reviewVersion, reviewId}) }}</span>
                    </div>
                    <div class="hubble-review-header__operate-body-btn">
                        <!-- <el-button class="hubble-workspace__title-btn">智能拆解</el-button> -->
                        <el-button :loading="reviewLoading" class="hubble-workspace__title-btn" @click="distributeReview">{{ $t('workspace.review.distribute') }}</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div class="hubble-manage-body">
            <div class="hubble-manage-body-left" ref="docContainer" id="docContainer">
                <el-collapse v-model="reviewVersionFileId" accordion @change="getPdfFile">
                    <el-collapse-item :name="file.reviewVersionFileId" v-for="(file, fileIndex) in versionFileInfos" :key="file.reviewVersionFileId">
                        <template slot="title">
                            <div class="file-header">
                                <div class="file-header-name">
                                    <img src="../../img/doc.png" alt="" class="doc-symbol" v-if="fileType(file.fileName) === 'doc'">
                                    <img src="../../img/docx.png" alt="" class="doc-symbol" v-if="fileType(file.fileName) === 'docx'" />
                                    <img src="../../img/pdf.png" alt="" class="doc-symbol" v-if="fileType(file.fileName) === 'pdf'" />
                                    <span>{{ file.fileName }}</span>
                                </div>
                                <div class="file-header-operate">
                                    <i class="el-icon-ssq-xiazai" @click.stop="downloadFile(file)"></i>
                                    <i class="el-icon-ssq--bs-shanchu" @click.stop="deleteFile(file.reviewVersionFileId)"></i>
                                    <div class="file-header-operate-order">
                                        <i class="el-icon-ssq-up" :class="{'order-first': fileIndex === 0}" @click.stop="sortOrder(file, 'up')"></i>
                                        <i class="el-icon-ssq-down" :class="{'order-last': fileIndex === versionFileInfos.length - 1}" @click.stop="sortOrder(file, 'down')"></i>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <div class="file-body" :ref="'review-documents-' + file.reviewVersionFileId" :id="'review-documents-' + file.reviewVersionFileId">
                            <Pdf
                                :style="styleObject"
                                :docWidth="docWidth"
                                :currentPageIndex="currentPageIndex"
                                :doc="curDoc"
                                parentElId="docContainer"
                                v-if="file.reviewVersionFileId === reviewVersionFileId"
                                @updateCurrentPageIndex="handleUpdatePageIndex"
                                @ready="handleReady"
                                @update-thumbnail="updateThumbnail"
                            >
                                <template slot-scope="index">
                                    <template v-for="(note, num) in filterNoteList(index.data)">
                                        <Filenote
                                            :key="num"
                                            :noteData="note"
                                            :workspaceId="workspaceId"
                                            :page="index.data"
                                            :isApprovenoteMove="isApprovenoteMove"
                                            @showDistribute="showDistribute"
                                            @deleteNote="deleteNote"
                                            @setApprovenoteMove="setApprovenoteMove"
                                        ></Filenote>
                                    </template>
                                </template>
                            </Pdf>
                        </div>
                    </el-collapse-item>
                </el-collapse>
                <el-upload
                    class="review-upload"
                    drag
                    :action="uploadUrl"
                    :show-file-list="false"
                    :on-success="handleSuccess"
                    :on-error="handleError"
                    :before-upload="beforeUpload"
                    v-loading="loading"
                    :headers="uploadHeaders"
                >
                    <div class="review-upload-text">
                        <i class="el-icon-ssq-shangchuan"></i>
                        <p>{{ $t('workspace.review.drag') }}</p>
                    </div>
                </el-upload>
            </div>
            <div class="hubble-manage-body-right">
                <p class="hubble-manage-body-right-title">{{ $t('workspace.review.content') }}</p>
                <el-tabs v-model="activeTab">
                    <el-tab-pane :label="$t('workspace.review.current')" name="current">
                        <div class="hubble-manage-body-right-content" v-for="(note, index) in distributeList" :key="index">
                            <p class="hubble-manage-body-right-content-title">
                                <span>{{ '《' + fileName(note.reviewVersionFileId) + '》' }}</span>
                                <span>{{ $t('workspace.review.current', {page: note.contentPosition.page}) }}</span>
                            </p>
                            <p class="hubble-manage-body-right-content-text">
                                {{ note.content }}
                            </p>
                            <div class="hubble-manage-body-right-content-user">
                                <p class="hubble-manage-body-right-content-user-title">{{ $t('workspace.review.users') }}：</p>
                                <p class="hubble-manage-body-right-content-user-list">{{ note.distributionUserAccounts }}</p>
                            </div>
                            <div class="hubble-manage-body-right-content-commit">
                                <div class="hubble-manage-body-right-content-commit-left">
                                    <span class="hubble-manage-body-right-content-commit">{{ $t('workspace.review.message') }}</span>
                                    <span class="hubble-manage-body-right-content-commit-detail">{{ note.comment }}</span>
                                </div>
                                <p class="hubble-manage-body-right-content-commit-right" @click="showDistribute(note)">
                                    <i class="el-icon-ssq-bianjibiaoqian"></i>
                                    {{ $t('workspace.review.modify') }}
                                </p>
                            </div>
                        </div>
                        <div class="hubble-manage-body-right-empty" v-if="!distributeList.length">
                            <i class="el-icon-ssq-meiyoujieguo1"></i>
                            <p>{{ $t('workspace.noData') }}</p>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('workspace.review.history')" name="history" v-if="fromDetail">
                        <div class="hubble-manage-body-right-content" v-for="(note, index) in historyList" :key="index">
                            <p class="hubble-manage-body-right-content-title">
                                <span>{{ '《' + fileName(note.reviewVersionFileId) + '》' }}</span>
                                <span>{{ $t('workspace.review.current', {page: note.contentPosition.page}) }}</span>
                            </p>
                            <p class="hubble-manage-body-right-content-text">
                                {{ note.content }}
                            </p>
                            <div class="hubble-manage-body-right-content-user">
                                <p class="hubble-manage-body-right-content-user-title">{{ $t('workspace.review.users') }}：</p>
                                <p class="hubble-manage-body-right-content-user-list">{{ note.distributionUserAccounts }}</p>
                            </div>
                            <div class="hubble-manage-body-right-content-commit">
                                <div class="hubble-manage-body-right-content-commit-left">
                                    <span class="hubble-manage-body-right-content-commit">{{ $t('workspace.review.message') }}</span>
                                    <span class="hubble-manage-body-right-content-commit-detail">{{ note.comment }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="hubble-manage-body-right-empty" v-if="!historyList.length">
                            <i class="el-icon-ssq-meiyoujieguo1"></i>
                            <p>{{ $t('workspace.noData') }}</p>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <DistributeDialog
            :distributeDialog="distributeDialog"
            :noteData="curNote"
            :workspaceId="workspaceId"
            :reviewId="reviewId"
            @closeDialog="distributeDialog = false"
            @saveTempDistribute="saveTempDistribute"
        ></DistributeDialog>
    </div>
</template>

<script>
// import 'pdfview/build/pdf.js';
// import workerSrc from 'pdfview/build/pdf.worker.js';
// import 'pdfview/web/pdf_viewer';
// import 'pdfview/web/pdf_viewer.css';
// PDFJS.workerSrc = workerSrc;
import { throttle } from 'utils/fn.js';
import { perviewPdf } from 'src/api/batchAgreement.js';
import Pdf from '../../components/pdf/Pdf.vue';
import Filenote from '../../components/filenote';
import DistributeDialog from '../../components/distributeDialog';
import { mapState } from 'vuex';
export default {
    components: {
        Pdf,
        Filenote,
        DistributeDialog,
    },
    data() {
        return {
            overviewDialog: false,
            // reviewId: '3718087511730990089',
            reviewId: '',
            reviewName: '',
            reviewVersion: '',
            reviewVersionId: '',
            versionFileInfos: [],
            reviewVersionFileId: '',
            docWidth: '',
            DPR: 2,
            isApprovenoteMove: false,
            curDoc: {
                page: [],
            },
            curNote: {},
            distributeDialog: false,
            scale: 1,
            currentPageIndex: 1,
            loading: false,
            noteList: [],
            distributeList: [],
            reviewLoading: false,
            activeTab: 'current',
            historyList: [],
            fromDetail: false,
            uploadHeaders: {
                'Authorization': `bearer ${this.$cookie.get('access_token')}`,
            },
        };
    },
    computed: {
        ...mapState(['workspaceId']),
        styleObject() {
            return {
                'margin-left': 0,
                'transform': `scale(1)`,
                'transform-origin': 'top center',
            };
        },
        // 文档的最大宽度
        maxWidth() {
            // 切换文档时，需要重新计算文档的最大宽高度
            const docMaxWidth = this.curDoc.page.reduce((max, page) => Math.max(max, page.width_init || page.width || 0), 0);
            return docMaxWidth;
        },
        uploadUrl() {
            return `/web-api/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/agreement`;
        },
        fileUrl() {
            return `/web-api/workspace/${this.workspaceId}/agreement-review/agreement/${this.reviewVersionFileId}?pdf=true`;
        },
        fileType() {
            return (fileName) => {
                const name = fileName.split('.');
                const type = name[name.length - 1];
                return type;
            };
        },
        filterNoteList() {
            return (val) => {
                const totalList = this.noteList.filter(note => note.reviewVersionFileId === this.reviewVersionFileId);
                const result = totalList.filter(item => item && (item.contentPosition.page === (val + 1)));
                return result;
            };
        },
        fileName() {
            return (fileId) => {
                const file = this.versionFileInfos.find(item => item.reviewVersionFileId === fileId);
                if (file) {
                    return file.fileName;
                } else {
                    return '';
                }
            };
        },
    },
    methods: {
        initAnnotate() {
            document.getElementById(`review-documents-${this.reviewVersionFileId}`).scrollTop = 0;
            const domList = document.getElementsByClassName('contract-page-content');
            if (!domList.length) {
                return setTimeout(() => {
                    this.initAnnotate();
                }, 100);
            }
            // 定义鼠标按下的坐标和鼠标抬起的坐标
            let startPos = { x: 0, y: 0 };
            let endPos = { x: 0, y: 0 };
            let isMouseDown = false;
            let moveDom = null;
            // 遍历每个dom，添加事件
            for (let i = 0; i < domList.length; i++) {
                const dom = domList[i];
                const rect = dom.getBoundingClientRect();
                let realTop;
                dom.addEventListener('mousedown', (event) => {
                    realTop = document.getElementById('docContainer').scrollTop - rect.top;
                    isMouseDown = true;
                    // 计算起始点百分比坐标
                    startPos = {
                        x: (event.clientX - rect.left) / rect.width,
                        y: (event.clientY + realTop) / rect.height,
                    };
                    endPos = {
                        x: (event.clientX - rect.left) / rect.width,
                        y: (event.clientY + realTop) / rect.height,
                    };
                    if (this.isApprovenoteMove) {
                        return;
                    }
                    moveDom = document.createElement('div');
                    moveDom.className = 'mgapprovenote__box disabled';
                    dom.appendChild(moveDom);
                });

                dom.addEventListener('mousemove', (event) => {
                    if (isMouseDown) {
                        // console.log('鼠标移动');
                        endPos = {
                            x: (event.clientX - rect.left) / rect.width,
                            y: (event.clientY + realTop) / rect.height,
                        };
                        if (this.isApprovenoteMove) {
                            return;
                        }
                        moveDom.style.top = `${Math.min(startPos.y, endPos.y) * 100}%`;
                        moveDom.style.left = `${Math.min(startPos.x, endPos.x) * 100}%`;
                        moveDom.style.width = `${Math.abs(startPos.x - endPos.x) * 100}%`;
                        moveDom.style.height = `${Math.abs(startPos.y - endPos.y) * 100}%`;
                    }
                });

                dom.addEventListener('mouseup', () => {
                    if (this.isApprovenoteMove) {
                        return;
                    }
                    isMouseDown = false;
                    const width = Math.abs(startPos.x - endPos.x);
                    const height = Math.abs(startPos.y - endPos.y);
                    if (width > 0 && height > 0) {
                        this.getReviewContent(this.reviewVersionFileId, {
                            x: Math.min(startPos.x, endPos.x),
                            y: Math.min(startPos.y, endPos.y),
                            width: width,
                            height: height,
                            page: i + 1,
                        }).then(res => {
                            const uniqueNum = new Date().getTime().toString();
                            this.noteList.push({
                                noteId: uniqueNum,
                                reviewVersionFileId: this.reviewVersionFileId,
                                content: res,
                                contentPosition: {
                                    x: Math.min(startPos.x, endPos.x),
                                    y: Math.min(startPos.y, endPos.y),
                                    width: width,
                                    height: height,
                                    page: i + 1,
                                },
                            });
                            // console.log(this.noteList);
                        });
                    }
                });
                document.addEventListener('mouseup', () => {
                    if (moveDom) {
                        moveDom && dom.removeChild(moveDom);
                        moveDom = null;
                    }
                });
            }
        },
        onDragMove: throttle(function(e) {
            e.preventDefault(); // 取消img默认拖拽事件
        }, 11),
        setApprovenoteMove(val) {
            this.isApprovenoteMove = val;
        },
        getAgreements() {
            return new Promise((resolve) => {
                this.$http.get(`/web-api/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/view-agreements`).then(res => {
                    const result = res.data.versionFileInfos;
                    if (!this.reviewName && result && result.length) {
                        this.reviewName = result[0].fileName;
                    }
                    result.sort((a, b) => a.fileOrder - b.fileOrder);
                    this.versionFileInfos = result;
                    this.reviewVersionId = res.data.reviewVersionId;
                    resolve(this.versionFileInfos);
                });
            });
        },
        beforeUpload(file) {
            const fileTypeArr = file.name.split('.');
            const fileType = fileTypeArr[fileTypeArr.length - 1];
            if (!['docx'].includes(fileType)) {
                this.$MessageToast.error(this.$t('workspace.review.uploadErrorMessage'));
                return false;
            }
            this.loading = true;
        },
        handleSuccess() {
            this.loading = false;
            this.getAgreements();
        },
        handleError(err) {
            this.$MessageToast.error(err.message);
            this.loading = false;
            this.getAgreements();
        },
        getPdfFile(id) {
            this.reviewVersionFileId = id;
            if (id) {
                this.docWidth = this.$refs.docContainer.getBoundingClientRect().width - 60;
                this.curDoc = {
                    pdfurl: this.fileUrl,
                    page: [],
                };
                this.$nextTick(() => {
                    this.initAnnotate();
                });
            }
        },
        downloadFile(file) {
            perviewPdf(this.workspaceId, file.reviewVersionFileId, { pdf: false }).then(res => {
                const a = document.createElement('a'); // 创建a标签
                a.style = 'display: none';
                a.download = file.fileName; // 设置下载文件名
                a.href = window.URL.createObjectURL(res.data); // 设置下载地址
                document.body.appendChild(a);
                a.click(); // 触发a标签的click事件
                document.body.removeChild(a);
            }).catch((err) => {
                console.error('Failed to download PDF:', err);
            });
        },
        deleteFile(reviewVersionFileId) {
            this.$http.post(`/web-api/workspace/${this.workspaceId}/agreement-review/agreement/delete/${reviewVersionFileId}`).then(() => {
                this.getAgreements();
            });
        },
        // 更新page的宽高信息
        handleReady(page) {
            this.$set(this.curDoc, 'page', page);
            this.$set(this.curDoc, 'hasParsed', true);
            this.updateScale();
        },
        updateThumbnail(thumbnail) {
            this.$set(this.curDoc, 'thumbnail', thumbnail);
            this.$set(this.versionFileInfos, this.currentDocIndex, {
                ...this.curDoc,
                thumbnail,
            });
        },
        handleUpdatePageIndex(currentPageIndex) {
            this.currentPageIndex = currentPageIndex;
        },
        /* 自定义签署位置相关 end */
        updateScale() {
            const { maxWidth, docWidth } = this;
            // console.log(docWidth, maxWidth);
            if (!maxWidth || !docWidth) {
                return this.scale = 1;
            }
            this.scale = docWidth / maxWidth;
        },
        showDistribute(data) {
            this.curNote = data;
            this.distributeDialog = true;
        },
        getReviewContent(reviewVersionFileId, data) {
            return new Promise((resolve) => {
                this.$http.post(`/web-api/workspace/${this.workspaceId}/agreement-review/content/${reviewVersionFileId}`, data).then(res => {
                    resolve(res.data.content);
                });
            });
        },
        saveTempDistribute(list) {
            // const length = this.noteList.length;
            const tempList = [];
            tempList.push(...this.historyList);
            this.noteList = [];
            list.forEach((note, index) => {
                const uniqueNum = new Date().getTime().toString() + index;
                tempList.push({
                    ...note,
                    noteId: uniqueNum,
                });
            });
            tempList.forEach((note, index) => {
                this.$set(this.noteList, index, note);
            });
            this.distributeList = list;
        },
        deleteNote(noteId) {
            const index = this.noteList.findIndex(note => note.noteId === noteId);
            const item = this.noteList[index];
            if (item.distributionIds) {
                this.$http.post(`/web-api/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/delete-distribute`, { distributionIds: item.distributionIds }).then(res => {
                    this.noteList.splice(index, 1);
                    this.distributeList = res.data;
                });
            } else {
                this.noteList.splice(index, 1);
            }
        },
        distributeReview() {
            this.reviewLoading = true;
            this.$http.post(`/web-api/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/start-review`, {
                reviewDetailUrl: `${window.location.origin}/hubble-workspace/review-details?workspaceId=${this.workspaceId}&review=${this.reviewId}`,
            }).then(() => {
                this.$MessageToast.success(this.$t('workspace.review.successInitiated'));
                this.$router.go(-1);
            }).finally(() => {
                this.reviewLoading = false;
            });
        },
        sortOrder(file, type) {
            const fileIndex = file.fileOrder - 1;
            if (fileIndex > 0 && fileIndex < this.versionFileInfos.length - 1 ||
                fileIndex === 0 && type === 'down' ||
                fileIndex === this.versionFileInfos.length - 1 && type === 'up'
            ) {
                const otherIndex = type === 'up' ? (fileIndex - 1) : (fileIndex + 1);
                const temp = this.versionFileInfos[fileIndex];
                const transitionClass = type === 'up' ? 'move-up' : 'move-down';
                const anotherTransitionClass = type === 'up' ? 'move-down' : 'move-up';
                const itemElements = document.querySelectorAll('.el-collapse-item');
                itemElements[fileIndex].classList.add(transitionClass);
                itemElements[otherIndex].classList.add(anotherTransitionClass);
                setTimeout(() => {
                    itemElements[fileIndex].classList.remove(transitionClass);
                    itemElements[otherIndex].classList.remove(anotherTransitionClass);
                    const fileOrder = temp.fileOrder;
                    this.versionFileInfos[fileIndex].fileOrder = this.versionFileInfos[otherIndex].fileOrder;
                    this.versionFileInfos[otherIndex].fileOrder = fileOrder;
                    this.$set(this.versionFileInfos, fileIndex, this.versionFileInfos[otherIndex]);
                    this.$set(this.versionFileInfos, otherIndex, temp);
                }, 500); // 动画持续时间，可根据实际调整
            } else {
                return;
            }
            const data = this.versionFileInfos.map(file => {
                return {
                    reviewVersionFileId: file.reviewVersionFileId,
                    order: file.fileOrder,
                };
            });
            this.$http.post(`/web-api/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/sort-agreement`, data).then(() => {
                // this.getAgreements();
            }).catch((err) => {
                this.$MessageToast.error(err.message);
            });
        },
        async getHistoryNote() {
            this.$http.get(`/web-api/workspace/${this.workspaceId}/agreement-review/${this.reviewId}/distribute-history`).then(res => {
                const tempList = [];
                res.data.forEach((note, index) => {
                    const uniqueNum = new Date().getTime().toString() + index;
                    tempList.push({
                        ...note,
                        noteId: uniqueNum,
                        history: true,
                    });
                });
                this.historyList = res.data.map(his => {
                    return {
                        ...his,
                        history: true,
                    };
                });
                this.noteList = JSON.parse(JSON.stringify(tempList));
            });
        },

    },
    created() {
        this.reviewId = this.$route.query.reviewId;
        this.reviewVersion = this.$route.query.versions ? this.$route.query.versions : '1';
        this.getAgreements().then(res => {
            if (res.length) {
                this.fromDetail = true;
                this.getHistoryNote();
            }
        });
    },
    async mounted() {
        document.addEventListener('mousemove', this.onDragMove);
        // this.docWidth = this.$refs.docContainer.getBoundingClientRect().width - 70;
        // this.docWidth = this.$refs.docContent.getBoundingClientRect().width;
        // this.curDoc = this.versionFileInfos[this.currentDocIndex];
        // this.cacheDocumentSize = document.body.clientWidth;
        // window.addEventListener('resize', this.handlePageResize);
        // // 默认进入的文档记录下来
        // this.previewedDocIds.push(this.curDoc.reviewVersionFileId);
        // this.updateScale();
        // this.initScale();
        // this.initAnnotate();
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.onDragMove);
        // window.removeEventListener('resize', this.handlePageResize);
    },
};
</script>

<style lang="scss" scoped>
.hubble-workspace {
    align-items: center;
    background: #f8f8f8;
    overflow-y: auto;
    &__header {
        width: 100%;
    }
}
.hubble-review-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    background-color: #fff;
    .hubble-review-header__title, .hubble-review-header__operate{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .hubble-review-header__title {
        border-bottom: 1px solid #eee;
        p {
            width: 80%;
            font-size: 18px;
            font-weight: 500;
            line-height: 16px;
            color: #000;
            padding: 22px 0;
        }
    }
    .hubble-review-header__operate-body {
        width: 80%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        &-text {
            display: flex;
            align-items: center;
            &-doc {
                font-size: 14px;
                font-weight: 500;
                line-height: 16px;
                color: #3D3D3D;
                margin-right: 10px;
            }
            &-code {
                font-size: 12px;
                line-height: 16px;
                color: #979797;
            }
        }
        &-btn {
            display: flex;
            align-items: center;
            .el-button {
                padding: 10px 20px;
                border-radius: 5px;
                margin-top: 0;
            }
        }
    }
}
.hubble-manage-body {
    width: 80%;
    display: flex;
    flex-grow: 1;
    padding-top: 22px;
    overflow: hidden;
    &-left {
        flex: 3;
        flex-shrink: 0;
        height: 100%;
        overflow-y: auto;
    }
    &-right {
        flex: 2;
        flex-shrink: 0;
        padding: 0 18px;
        height: 100%;
        overflow-y: auto;
        &-title {
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 500;
            line-height: 16px;
            color: #3D3D3D;
        }
        &::v-deep .el-tabs {
            height: calc(100% - 40px);
            .el-tabs__content {
                height: calc(100% - 57px);
                .el-tab-pane {
                    height: 100%;
                    overflow-y: auto;
                }
            }
        }
        &-content {
            border-radius: 5px;
            background: #FFFFFF;
            padding: 15px;
            margin-bottom: 10px;
            &-title {
                display: flex;
                align-items: center;
                color: #0988EC;
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                margin-bottom: 9px;
                span:last-of-type {
                    color: #3D3D3D;
                }
            }
            &-text {
                border-radius: 8px;
                background-color: #F8F8F8;
                padding: 10px 12px;
                font-size: 12px;
                font-weight: normal;
                line-height: 20px;
                color: #666666;
                margin-bottom: 13px;
            }
            &-user {
                line-height: 18px;
                font-size: 12px;
                padding-bottom: 9px;
                margin-bottom: 9px;
                border-bottom: 1px solid #D8D8D8;
                &-user {
                    font-weight: 500;
                }
            }
            &-commit {
                display: flex;
                justify-content: space-between;
                align-items: center;
                &-left {
                    font-size: 12px;
                    span:first-of-type {
                        font-weight: 500;
                    }
                }
                &-right {
                    padding: 4px 10px;
                    background: #F1F9FF;
                    border-radius: 20px;
                    font-size: 12px;
                    line-height: 16px;
                    color: #0988EC;
                }
            }
        }
        &-empty {
            height: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #ccc;
            text-align: center;
            border-radius: 4px;
            i {
                font-size: 70px;
                margin-bottom: 20px;
            }
        }
    }
    &::v-deep .el-collapse {
        border: none;
        &-item {
            margin-bottom: 10px;
            border-radius: 4px;
            overflow: hidden;
            &__arrow {
                display: none;
            }
            &__header {
                border: none;
                padding: 10px 18px;
                height: auto;
                .file-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 100%;
                    &-name {
                        display: flex;
                        align-items: center;
                        .doc-symbol {
                            width: 25px;
                            height: auto;
                            margin-right: 10px;
                        }
                    }
                    &-operate {
                        display: flex;
                        align-items: center;
                        font-size: 18px;
                        i {
                            margin-right: 26px;
                            color: #979797;
                        }
                        &-order {
                            display: flex;
                            align-items: center;
                            flex-direction: column;
                            font-size: 7px;
                            i {
                                color: #ccc;
                                &:first-of-type {
                                    margin-bottom: 4px;
                                }
                            }
                            .el-icon-ssq-up:hover, .el-icon-ssq-down:hover {
                                color: #666;
                            }
                            .order-first:hover, .order-last:hover {
                                color: #ccc!important;
                                cursor: default;
                            }
                        }
                    }
                }
            }
            &__content {
                background-color: #eee;
            }
            .file-body {
                width: 100%;
                // max-height: 50vh;
                overflow-y: auto;
            }
        }
    }
}

.review-upload::v-deep .el-upload {
    width: 100%;
    .el-upload-dragger {
        width: 100%;
        background-color: #EEEEEE;
        border-color: #B5B5B5;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30vh;
        min-height: 180px;
        .review-upload-text {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            i {
                margin-right: 15px;
            }
        }
    }
}
.move-up {
  animation: moveUp 0.5s ease;
}
.move-down {
  animation: moveDown 0.5s ease;
}

@keyframes moveUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-63px); /* 根据视觉效果调整移动距离 */
  }
}

@keyframes moveDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(63px); /* 根据视觉效果调整移动距离 */
  }
}
</style>
