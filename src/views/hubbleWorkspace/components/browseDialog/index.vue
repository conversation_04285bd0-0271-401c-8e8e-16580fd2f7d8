<template>
    <el-dialog
        :title="`${title} ${$t('workspace.review.overview')}`"
        :visible.sync="browseDialog"
        width="60%"
        class="hubble-workspace__dialog"
        :before-close="closeDialog"
    >
        <div class="hubble-workspace__dialog-body">
            <div class="hubble-workspace__dialog-body-item" v-for="item in list" :key="item.id">
                <div class="hubble-workspace__dialog-body-item-top">
                    <p class="hubble-workspace__dialog-body-item-top-name">{{ item.reviewerName }}</p>
                    <span
                        v-if="item.reviewResult"
                        class="hubble-workspace__dialog-body-item-top-manner"
                        :class="item.reviewResult"
                    >{{ $t(`workspace.review.${item.reviewResult}`) }}</span>
                </div>
                <!-- <p class="hubble-workspace__dialog-body-item-else">采购主管</p> -->
                <p class="hubble-workspace__dialog-body-item-else">{{ $t('workspace.review.recent') + renderDate(item.replyTime) }}</p>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import dayjs from 'dayjs';
export default {
    props: {
        browseDialog: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        list: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {};
    },
    computed: {
        renderDate() {
            return (stamp) => {
                if (!stamp) {
                    return '暂未审查';
                }
                return dayjs(stamp).format('YYYY-MM-DD  HH:mm:ss');
            };
        },
    },
    watch: {},
    methods: {
        closeDialog() {
            this.$emit('closeDialog');
        },
    },
    created() {},
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
    padding: 15px 20px;
}
::v-deep .el-dialog__body {
  padding: 20px;
  background: #F9F9F9;
}
.hubble-workspace__dialog-body {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    flex-wrap: wrap;
    &-item {
        width: 32%;
        padding: 16px;
        margin-bottom: 20px;
        border-radius: 5px;
        background-color: #fff;
        margin-right: 1%;
        &-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            &-name {
                font-size: 12px;
                font-weight: 500;
                line-height: 24px;
                color: #3D3D3D;
            }
            &-manner {
                border-radius: 15px;
                font-size: 10px;
                font-weight: normal;
                line-height: 12px;
                color: #FFFFFF;
                padding: 2px 5px;
            }
            .PASS {
                background: #0988EC;
            }
            .NOT_PASS {
                background: #FC783B;
            }
        }
        &-else {
            font-size: 10px;
            line-height: 16px;
            color: #666666;
        }
    }
}
</style>
