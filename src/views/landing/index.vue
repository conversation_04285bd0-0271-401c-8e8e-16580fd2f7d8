<template>
    <div class="landing">
        <header class="landing__header">
            <div class="landing__header-left">
                <img class="landing__logo" :src="require('img/bestsign-logo-en.png')" alt="Hubble" />
            </div>
            <nav class="landing__nav">
                <a href="javascript:void(0)" @click="scrollTo('features')">{{ $t('landing.navFeatures') }}</a>
                <a href="javascript:void(0)" @click="scrollTo('scenarios')">{{ $t('landing.navScenarios') }}</a>
                <a href="javascript:void(0)" @click="scrollTo('advantages')">{{ $t('landing.navAdvantages') }}</a>
            </nav>
            <LangSwitch cacheOnly></LangSwitch>
        </header>

        <section class="landing__hero">
            <div class="landing__hero-inner">
                <h1 class="landing__title">{{ $t('landing.heroTitle') }}</h1>
                <p class="landing__subtitle">{{ $t('landing.heroSubtitle') }}</p>
                <div class="landing__cta">
                    <el-button class="landing__btn-primary" size="medium" @click="goRiskJudge">{{ $t('landing.ctaStart') }}</el-button>
                </div>
            </div>
        </section>

        <section ref="features" class="landing__features">
            <h2 class="landing__section-title">{{ $t('landing.featuresTitle') }}</h2>
            <div class="landing__card-grid">
                <div class="landing__card">
                    <img :src="require('views/hubbleWorkspace/img/svg/search.svg')" alt="" />
                    <h3>{{ $t('landing.f1Title') }}</h3>
                    <p>{{ $t('landing.f1Desc') }}</p>
                </div>
                <div class="landing__card">
                    <img :src="require('views/hubbleWorkspace/img/svg/format.svg')" alt="" />
                    <h3>{{ $t('landing.f2Title') }}</h3>
                    <p>{{ $t('landing.f2Desc') }}</p>
                </div>
                <div class="landing__card">
                    <img :src="require('views/hubbleWorkspace/img/svg/finished.svg')" alt="" />
                    <h3>{{ $t('landing.f3Title') }}</h3>
                    <p>{{ $t('landing.f3Desc') }}</p>
                </div>
            </div>
        </section>

        <section ref="scenarios" class="landing__scenarios">
            <h2 class="landing__section-title">{{ $t('landing.scenariosTitle') }}</h2>
            <div class="landing__card-grid">
                <div class="landing__card">
                    <img :src="''" alt="" />
                    <h3>{{ $t('landing.s1Title') }}</h3>
                    <p>{{ $t('landing.s1Desc') }}</p>
                </div>
                <div class="landing__card">
                    <img :src="require('views/hubbleWorkspace/img/svg/search.svg')" alt="" />
                    <h3>{{ $t('landing.s2Title') }}</h3>
                    <p>{{ $t('landing.s2Desc') }}</p>
                </div>
                <div class="landing__card">
                    <img :src="require('views/hubbleWorkspace/img/svg/proofread.svg')" alt="" />
                    <h3>{{ $t('landing.s3Title') }}</h3>
                    <p>{{ $t('landing.s3Desc') }}</p>
                </div>
            </div>
        </section>

        <section ref="advantages" class="landing__advantages">
            <h2 class="landing__section-title">{{ $t('landing.advantagesTitle') }}</h2>
            <div class="landing__card-grid">
                <div class="landing__card">
                    <img :src="require('views/hubbleWorkspace/img/svg/finished.svg')" alt="" />
                    <h3>{{ $t('landing.a1Title') }}</h3>
                    <p>{{ $t('landing.a1Desc') }}</p>
                </div>
                <div class="landing__card">
                    <img :src="require('views/hubbleWorkspace/img/svg/format.svg')" alt="" />
                    <h3>{{ $t('landing.a2Title') }}</h3>
                    <p>{{ $t('landing.a2Desc') }}</p>
                </div>
                <div class="landing__card">
                    <img :src="require('views/hubbleWorkspace/img/svg/proofread.svg')" alt="" />
                    <h3>{{ $t('landing.a3Title') }}</h3>
                    <p>{{ $t('landing.a3Desc') }}</p>
                </div>
            </div>
        </section>

        <section class="landing__cta-bottom">
            <h2>{{ $t('landing.bottomCtaTitle') }}</h2>
            <el-button class="landing__btn-primary" size="medium" @click="goRiskJudge">{{ $t('landing.ctaStart') }}</el-button>
        </section>

        <footer class="landing__footer">
            <span>© {{ new Date().getFullYear() }} Hubble</span>
            <span class="landing__footer-sep">·</span>
            <a href="mailto:<EMAIL>"><EMAIL></a>
        </footer>
    </div>
</template>

<script>
import LangSwitch from 'components/langSwitch/index.vue';

export default {
    components: { LangSwitch },
    methods: {
        goRiskJudge() {
            this.$router.push('/contract-risk-judgement');
        },
        scrollTo(refName) {
            try {
                const target = this.$refs[refName];
                target && target.scrollIntoView({ behavior: 'smooth', block: 'start' });
            } catch (e) { /* no-op */ }
        },
    },
};
</script>

<style lang="scss" scoped>
.landing{
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    color: #111;
    *{ box-sizing: border-box; }

    &__header{
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 24px;
        border-bottom: 1px solid #fae0cf;
        .lang-switch{
            color: #e06421 !important;
        }
    }
    &__logo{
        height: 50px;
        display: block;
    }
    &__nav{
        display: flex;
        gap: 24px;
        a{ color: #e06421; text-decoration: none; font-size: 14px; }
        a:hover{ color: #ff7a2b; }
    }

    &__hero{
        background: #fff7f0;
        padding: 72px 24px 56px;
    }
    &__hero-inner{ max-width: 960px; margin: 0 auto; text-align: left; }
    &__title{
        font-size: 44px;
        line-height: 1.2;
        margin: 0 0 12px;
        color: #ff6a00;
        font-weight: 800;
    }
    &__subtitle{
        font-size: 16px;
        color: #a4552a;
        margin-bottom: 20px;
    }
    &__cta{
        display: flex;
        gap: 12px;
    }
    &__btn-primary{ background: #ff6a00; border-color: #ff6a00; color: #fff; }
    &__btn-primary:hover{ background: #ff7a2b; border-color: #ff7a2b; }

    &__features{
        padding: 48px 24px 24px;
        background: #fff;
    }
    &__section-title{
        font-size: 28px;
        text-align: center;
        margin: 0 0 24px;
        color: #ff6a00;
        font-weight: 700;
    }
    &__card-grid{ display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; max-width: 1120px; margin: 0 auto; }
    &__card{ background: #fff7f0; border: 1px solid #fae0cf; border-radius: 12px; padding: 18px; text-align: left; }
    &__card img{ height: 28px; margin-bottom: 10px; }
    &__card h3{ font-size: 16px; margin: 0 0 8px; color: #e06421; }
    &__card p{ font-size: 13px; color: #6e4d3c; margin: 0; }

    &__scenarios{ padding: 48px 24px; background: #fff7f0; }
    &__advantages{ padding: 48px 24px; background: #fff; }

    &__cta-bottom{
        text-align: center;
        padding: 40px 24px 56px;
        background: #fff7f0;
        h2{ font-size: 24px; margin: 0 0 16px; color: #ff6a00; }
    }

    &__footer{
        margin-top: auto;
        padding: 16px 24px;
        border-top: 1px solid #fae0cf;
        background: #fff;
        text-align: center;
        color: #a47f6d;
        font-size: 12px;
    }
    &__footer a{ color: #a47f6d; text-decoration: none; }
    &__footer-sep{ margin: 0 6px; }

    @media (max-width: 920px){
        &__card-grid{ grid-template-columns: repeat(2, 1fr); }
        &__hero-inner{ text-align: center; }
    }
    @media (max-width: 520px){
        &__card-grid{ grid-template-columns: 1fr; }
        &__title{ font-size: 32px; }
    }
}
</style>

