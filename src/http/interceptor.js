/* eslint-disable eqeqeq */
// 当前版本 64bdc3640daaa38c61157ee11a038b23ea287be9 Tue Mar 17 17:37:24 2020 +0800
// 提测之前需比对下版本
import Vue from 'vue';
import axios from 'axios';
import cookie from 'vue-cookie';
import auth0  from 'plugins/auth0/index';
import { codeHandler } from './codeHandler.js';
import router from 'src/router';

axios.defaults.baseURL = `${window.location.protocol}//${window.location.host}`;
// axios request 拦截器
/**
 * config 动态配置参数
 * noToast: 1, 接口返回非200去除默认toast
 * noCodeHandler: true, 接口返回409去除默认错误映射弹窗
 * noUseToken: true, 接口请求中header不使用Authorization,使用场景：混合云3
 */
axios.interceptors.request.use(config => {
    const access_token = cookie.get('access_token');
    // 避免混3上传config中传headers.Authorization
    if (config.noUseToken && config.headers && config.headers.Authorization) {
        delete config.headers.Authorization;
    }
    if (access_token && !config.noUseToken) {
        config.headers.Authorization = `bearer ${access_token}`;
    }
    // 给 get 请求加上时间戳防缓存
    if (config.method === 'get') {
        config.url += `${config.url.indexOf('?') > -1 ? '&' : '?'}_t=${new Date().getTime()}`;
    }
    // 在config中记录请求发起的时间
    config.requestTime = new Date().getTime();
    return config;
});
axios.interceptors.response.use(response => {
    response.responseTime = new Date().getTime();
    return response;
}, error => {
    const responseTime = new Date().getTime();
    if (error.response) {
        // 请求成功发出且服务器也响应了状态码
        error.response.responseTime = responseTime;
        if (error.response.status === 401) {
            return handle401Response(error);
        }
        handleResponseError(error);
        return Promise.reject(packingCustomGeneralError(error, responseTime));
    } else {
        return Promise.reject(packingCustomGeneralError(error, responseTime));
    }
});

async function handle401Response(error) {
    const { config } = error;
    // 如果是本地开发，并且本地是localhost、127.0.0.1打开，通过dialog登陆
    // if (process.env.NODE_ENV.includes('development') && (location.host.indexOf('localhost') > -1 || location.host.indexOf('127.0.0.1') > -1 || location.host.indexOf('192.168.') > -1) && location.port !== '6004') {
    //     loginByPassword();
    //     return Promise.reject(error);
    // }
    // 如果access_token过期，则请求刷新token接口，然后重新调用原接口
    if (!config._retry) {
        config._retry = true;
        const isAuthenticated = await Vue.$auth0.isAuthenticated();
        console.log(isAuthenticated);
        if (isAuthenticated) {
            const token = await Vue.$auth0.getTokenSilently();
            console.log(token);
            Vue.$cookie.set('access_token', token);
            axios.defaults.headers.common['Authorization'] = `bearer ${token}`;
            config.headers['Authorization'] = temp;
            return axios(config);
        }
        Vue.$auth0.loginWithRedirect({
            appState: {
                targetPath: location.pathname,
            },
        });
    } else {
        return Promise.reject(packingCustomGeneralError(error, error.response.responseTime));
    }
}
function handleResponseError(error) {
    const {
        config,
        response: {
            status,
            data,
        },
    } = error;
    const code = data && data.code;
    const message = !config.noToast && data?.message;

    // 排除免处理请求noCodeHandler:true
    if (!config.noCodeHandler) {
        codeHandler(code, data, message, error, status);
    }
}

// 服务器未响应时，封装一个供业务使用的Object对象
// 原来会返回一个Error Type对象，不友好
function packingCustomGeneralError(error, responseTime) {
    // 可以在这里处理不同类型的error
    // 原始throw出来的error对象，包含name、message、stack属性
    // console.log(error instanceof Error);
    // console.log(error instanceof TypeError);

    let result = {
        responseTime: responseTime,
        response: error.response,
        status: error.name,
        statusText: error.message,
        config: error.config,
        request: error.request, // `error.request` 在浏览器中是 XMLHttpRequest 的实例
        name: error.name, // error对象的默认属性
        message: error.message, // error对象的默认属性
        stack: error.stack, // error对象的默认属性
    };

    // 一般40x的请求会有response，50x的没有
    if (error.response) {
        result = {
            ...result,
            status: error.response.status, // 请求的http code
            statusText: error.response.data.message || error.message, // 后端有response错误提示则用后端的错误提示
        };
    }
    return result;
}

axios.install = function(Vue) {
    Vue.$http = axios;
    Vue.prototype.$http = axios;
};

export default axios;
