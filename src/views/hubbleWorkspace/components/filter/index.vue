<template>
    <div class="hubble-workspace__filter">
        <el-button class="hubble-workspace__filter-btn" :type="showDialog ? 'primary' : 'default'" @click="showDialog = !showDialog">
            <i class="el-icon-ssq-guolv"></i>
            {{ $t('filter.filter') }}
        </el-button>
        <el-button class="hubble-workspace__filter-btn" @click="() => $emit('changeOperate', 'restart')" :disabled="!agreementIds.length">
            <i class="el-icon-ssq-icon_refresh"></i>
            {{ $t('filter.refreshExtraction') }}
        </el-button>
        <el-button class="hubble-workspace__filter-btn" @click="() => $emit('changeOperate', 'terms')" :disabled="!agreementIds.length">
            {{ $t('filter.extractTerms') }}
        </el-button>
        <el-button class="hubble-workspace__filter-btn" @click="() => $emit('refresh')">
            {{ $t('filter.refreshList') }}
        </el-button>
        <div class="hubble-workspace__filter-mask" @click="showDialog = false" v-show="showDialog"></div>
        <ul class="hubble-workspace__filter-dialog" v-show="showDialog">
            <li>{{ $t('filter.currentCondition') }}</li>
            <li v-for="(option, i) in options" :key="i">
                <div class="hubble-workspace__filter-select select-1" v-if="i === 0">{{ $t('filter.when') }}</div>
                <el-select v-else-if="i === 1" v-model="andOrType" class="hubble-workspace__filter-select select-1">
                    <el-option v-for="filter in filters1" :key="filter.value" :label="filter.label" :value="filter.value"></el-option>
                </el-select>
                <div class="hubble-workspace__filter-select select-1" v-else></div>
                <el-select v-model="option.termId" class="hubble-workspace__filter-select" @change="setFieldType($event, i)">
                    <el-option v-for="filter in fieldList" :key="filter.termId" :label="filter.fieldName" :value="filter.termId"></el-option>
                </el-select>
                <el-select v-model="option.operatorType" class="hubble-workspace__filter-select">
                    <el-option v-for="filter in curFilters2(option.fieldType)" :key="filter.value" :label="filter.label" :value="filter.value"></el-option>
                </el-select>
                <div class="hubble-workspace__filter-select select-3">
                    <el-select v-if="option.fieldType === 'BOOLEAN'" v-model="option.fieldValue" :placeholder="$t('filter.selectCondition')">
                        <el-option :label="$t('filter.yes')" :value="true"></el-option>
                        <el-option :label="$t('filter.no')" :value="false"></el-option>
                    </el-select>
                    <el-date-picker v-else-if="option.fieldType === 'DATE'" v-model="option.fieldValue" type="date" value-format="yyyy-MM-dd" :placeholder="$t('filter.selectCondition')"></el-date-picker>
                    <el-input v-else :type="option.fieldType === 'NUMBER' ? 'number' : 'text'" :placeholder="$t('filter.enterCondition')" v-model="option.fieldValue"></el-input>
                </div>
                <i class="el-icon-ssq--bs-shanchu hubble-workspace__filter-delete" @click="deleteOption(i)" v-show="options.length > 1"></i>
            </li>
            <li>
                <span class="ThemeColor" @click="addOption">+ {{ $t('filter.addCondition') }}</span>
                <div>
                    <el-button @click="handleReset">{{ $t('filter.reset') }}</el-button>
                    <el-button type="primary" @click="handleSearch">{{ $t('filter.confirm') }}</el-button>
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    props: {
        fieldList: {
            type: Array,
            default: () => {},
        },
        agreementIds: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            showDialog: false,
            andOrType: '1',
            options: [{ termId: '', operatorType: '1', andOrType: '1', fieldValue: '' }],
            filters1: [
                { label: this.$t('filter.and'), value: '1' },
                { label: this.$t('filter.or'), value: '2' },
            ],
        };
    },
    methods: {
        curFilters2(fieldType) {
            const baseFilter = [
                { label: this.$t('filter.equals'), value: '1' },
                { label: this.$t('filter.notEquals'), value: '2' },
            ];
            const textFilter = [
                { label: this.$t('filter.contains'), value: '7' },
                { label: this.$t('filter.notContains'), value: '8' },
            ];
            const otherFilter = [
                { label: this.$t('filter.greaterThan'), value: '3' },
                { label: this.$t('filter.greaterThanOrEquals'), value: '4' },
                { label: this.$t('filter.lessThan'), value: '5' },
                { label: this.$t('filter.lessThanOrEquals'), value: '6' },
            ];
            if (fieldType === 'BOOLEAN') {
                return baseFilter;
            } else if (['LONG_TEXT', 'TEXT'].includes(fieldType)) {
                return [
                    ...baseFilter,
                    ...textFilter,
                ];
            }
            return [
                ...baseFilter,
                ...otherFilter,
            ];
        },
        handleReset() {
            this.options = [{ termId: '', operatorType: '1', andOrType: '1', fieldValue: '' }];
        },
        deleteOption(index) {
            this.options.splice(index, 1);
        },
        setFieldType(termId, index) {
            this.$set(this.options, index, {
                ...this.options[index],
                fieldType: this.fieldList.find(el => el.termId === termId)?.fieldType,
                fieldValue: '',
            });
        },
        isText(type) {
            return ['NUMBER', 'TEXT', 'LONG_TEXT'].includes(type);
        },
        addOption() {
            this.options.push({ termId: '', operatorType: '1', andOrType: '1', fieldValue: '' });
        },
        handleSearch() {
            const firstItem = this.options[0];
            if ((firstItem.termId || this.options.length > 1) && this.options.some(el => el.fieldValue === '')) {
                return this.$MessageToast(this.$t('filter.emptyCondition'));
            }
            this.showDialog = false;
            this.$emit('change', this.options.filter(el => el.fieldValue !== '').map(el => {
                el.andOrType = this.andOrType;
                return el;
            }));
        },
    },
};
</script>

<style lang="scss">
.hubble-workspace__filter{
    position: relative;
    &-mask{
        position: fixed;
        width: 100vw;
        height: 100vh;
        top: 0;
        left: 0;
        z-index: 9;
    }
    &-dialog{
        width: 600px;
        padding: 25px 30px;
        position: absolute;
        z-index: 99;
        top: 53px;
        background: #fff;
        border-radius: 5px;
        box-shadow: 0px 4px 16px 0px rgba(55, 59, 62, 0.1);
        li{
            font-size: 14px;
            line-height: 36px;
            display: flex;
            justify-content: space-between;
            &+li{
                margin-top: 10px;
            }
        }
    }
    &-select{
        width: 120px;
        input{
            background: #f8f8f8;
            border-radius: 5px !important;
            border: none;
        }
        &.select-1{
            width: 36px;
            input{
                background: transparent;
                padding: 0;
            }
            .el-input__icon{
                right: -5px;
            }
        }
        &.select-3{
            width: 200px;
            .el-input{
                width: 100%;
            }
        }
    }
    &-delete{
        color: #666;
        padding: 10px;
        background: #f8f8f8;
        cursor: pointer;
    }
}
</style>
