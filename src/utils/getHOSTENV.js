/**
 * @return {String}
 * @description 当前是建行还是其他
 */
export function getHOSTENV() {
    let HOST_ENV = 'official';
    if (
        process.env.NODE_ENV === 'dev-ccb' ||
        location.host === 'ccb.bestsign.cn' ||
        location.host === 'ccb.bestsign.info'
    ) {
        HOST_ENV = 'CCB';
    }
    return HOST_ENV;
}

/**
 * @description 当前是正式环境还是预发布环境(之后如果需要更详细的判断dev环境等，可以在后面加别的判断条件)
 */
export function getPresentEnv() {
    const hostName = window.location.host;
    let ENV_NAME = 'PE_ENV';
    if (hostName.includes('.info')) {
        ENV_NAME = 'PRE_ENV';
    }
    return ENV_NAME;
}
