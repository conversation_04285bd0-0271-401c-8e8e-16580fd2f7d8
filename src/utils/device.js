/* eslint-disable no-cond-assign */
const userAgentInfo = navigator.userAgent;

export const isPC = function() {
    const isMobile = userAgentInfo.match(/Android/i) ||
        userAgentInfo.match(/webOS/i) ||
        userAgentInfo.match(/iPhone/i) ||
        userAgentInfo.match(/iPad/i) ||
        userAgentInfo.match(/iPod/i) ||
        userAgentInfo.match(/BlackBerry/i) ||
        userAgentInfo.match(/Windows Phone/i);
    return !isMobile;
};
// android终端
export const isAndroid = userAgentInfo.indexOf('Android') > -1 || userAgentInfo.indexOf('Adr') > -1;
// ios终端
export const isIOS = !!userAgentInfo.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

// ios safari
export const isIOSSafari = isIOS && !!userAgentInfo.match(/WebKit/i) && !userAgentInfo.match(/CriOS/i);

// 微信
export const isWechat = userAgentInfo.toLowerCase().includes('micromessenger');

export const isQyWechat = isWechat && userAgentInfo.toLowerCase().includes('wxwork');

// 支付宝 app
export const isAliPay = /Alipay/i.test(userAgentInfo);

// 钉钉
export const isDingTalk = userAgentInfo.includes('DingTalk');
// 是否在微信小程序环境下
export const isMiniprogram = isWechat && userAgentInfo.toLowerCase().includes('miniprogram');

const versionRegType = {
    android: /android\s([0-9\.]*)/,
    ios: /cpu.+os (.*?) like mac os/,
    wechat: /micromessenger\/([\d.]+)/i,
};
const getVersion = (type) => {
    return userAgentInfo.toLowerCase().match(versionRegType[type]);
};
// android 版本号
export const androidVersion = getVersion('android') ? getVersion('android')[1] : 0;
// ios 版本号
export const IOSVersion = getVersion('ios') ? getVersion('ios')[1].replace(/_/g, '.') : 0;
// 微信版本号
export const wechatVersion = getVersion('wechat') ? getVersion('wechat')[1] : 0;
// export const browserName = function() {
//     let userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
//     let isOpera = userAgent.indexOf("Opera") > -1;
//     if (isOpera) {
//         return "Opera"
//     }; //判断是否Opera浏览器
//     if (userAgent.indexOf("Firefox") > -1) {
//         return "Firefox";
//     } //判断是否Firefox浏览器
//     if (userAgent.indexOf("Chrome") > -1) {
//         return "Chrome";
//     }
//     if (userAgent.indexOf("Safari") > -1) {
//         return "Safari";
//     } //判断是否Safari浏览器
//     if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
//         return "IE"; // 有问题
//     }; //判断是否IE浏览器
// }

export const isIE = function() {
    const ua = window.navigator.userAgent;
    const msie = ua.indexOf('MSIE ');
    if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) { // If Internet Explorer, return version number
        return true;
        // return parseFloat( ua.substring(msie + 5, ua.indexOf(".", msie)) );
    } else { // If another browser, return 0
        return false;
    }
};

// 是否是华为手机
export const isHUAWEI = function() {
    const ua = window.navigator.userAgent;
    if (ua.includes('HUAWEI') || ua.includes('HONOR') || ua.includes('Honor')) {
        return true;
    } else {
        return false;
    }
};

export function CloseWebPage() {
    if (navigator.userAgent.indexOf('MSIE') > 0) {  // close IE
        if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
            window.opener = null;
            window.close();
        } else {
            window.open('', '_top');
            window.top.close();
        }
    } else if (navigator.userAgent.indexOf('Firefox') > 0) { // close firefox
        window.location.href = 'about:blank ';
        window.close();
    } else { // close chrome;It is effective when it is only one
        window.opener = null;
        window.open('', '_self', '');
        window.close();
    }
    // 如果无法关闭改为空白页
    if (window) {
        window.location.href = 'about:blank';
    }
    // window.history.back(); //部分手机
}

export const getBroswer = function() {
    var Sys = {};
    var ua = navigator.userAgent.toLowerCase();
    var s;
    (s = ua.match(/edge\/([\d.]+)/)) ? Sys.edge = s[1]
        : (s = ua.match(/rv:([\d.]+)\) like gecko/)) ? Sys.ie = s[1]
            : (s = ua.match(/msie ([\d.]+)/)) ? Sys.ie = s[1]
                : (s = ua.match(/firefox\/([\d.]+)/)) ? Sys.firefox = s[1]
                    : (s = ua.match(/chrome\/([\d.]+)/)) ? Sys.chrome = s[1]
                        : (s = ua.match(/opera.([\d.]+)/)) ? Sys.opera = s[1]
                            : (s = ua.match(/version\/([\d.]+).*safari/)) ? Sys.safari = s[1] : 0;

    if (Sys.edge) {
        return { broswer: 'Edge', version: Sys.edge };
    }
    if (Sys.ie) {
        return { broswer: 'IE', version: Sys.ie };
    }
    if (Sys.firefox) {
        return { broswer: 'Firefox', version: Sys.firefox };
    }
    if (Sys.chrome) {
        return { broswer: 'Chrome', version: Sys.chrome };
    }
    if (Sys.opera) {
        return { broswer: 'Opera', version: Sys.opera };
    }
    if (Sys.safari) {
        return { broswer: 'Safari', version: Sys.safari };
    }

    return { broswer: '', version: '0' };
};

export const isChrome = function() {
    return getBroswer().broswer.toLowerCase() === 'chrome';
};

export const isSafari = function() {
    return /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
};

/**
 * @description: 比较两个版本号
 * @param t: 目前版本 e: 对比版本
 * @return 1: 目前版本较高; -1: 目前版本较低
 */
export const compareVersion = (t, e) => {
    t = t.split('.');
    e = e.split('.');
    const n = Math.max(t.length, e.length);
    for (; t.length < n;) {
        t.push('0');
    }
    for (; e.length < n;) {
        e.push('0');
    }
    for (let r = 0; r < n; r++) {
        const n = parseInt(t[r]);
        const o = parseInt(e[r]);
        if (n > o) {
            return 1;
        }
        if (n < o) {
            return -1;
        }
    }
    return 0;
};
