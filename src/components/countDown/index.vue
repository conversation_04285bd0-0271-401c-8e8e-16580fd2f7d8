<!-- 通用组件：获取验证码倒计时 -->
<!-- 引用位置 业务组件：AccountPopChangeAccount,AccountPopChangeInform，AccountPopResetPassword-->
<template>
    <button class="count-down brand-color"
        :disabled="disabled || time > 0"
        @click.prevent="clickedFn"
        :class="{active: time > 0}"
    >
        {{ text }}
    </button>
</template>
<script>
export default {
    name: 'CountDown',
    props: {
        isWap: {
            type: Boolean,
            default: false,
        },
        second: {
            type: Number,
            default: 60,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clickedFn: {
            type: Function,
            required: true,
        },
        runFlag: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            time: 0,
        };
    },
    computed: {
        text: function() {
            if (this.isWap) {
                return this.time > 0 ? `${this.$t('countDown.resendCode')}(${this.time}s)` : this.$t('countDown.getVerifyCode');
            } else {
                return this.time > 0 ? `${this.time}s` : this.$t('countDown.getVerifyCode');
            }
        },
    },
    watch: {
        runFlag: function(val) {
            if (val) {
                this.run();
            }
        },
    },
    methods: {
        reset() {
            this.time = 0;
        },
        run() {
            this.time = this.second;
            this.timer();
        },
        timer() {
            const inTimer = setInterval(() => {
                if (this.time > 0) {
                    this.time--;
                } else {
                    clearInterval(inTimer);
                }
            }, 1000);
        },
    },
};
</script>
<style lang="scss">
	.count-down {
		background-color: $--background-color-base;
		color: $--color-primary;
        border: 1px solid $--border-color-base;
        font-size: 14px;
        text-align: center;
        cursor: pointer;
        border-radius: 1px;
        line-height: 1;
        &:hover {
            background-color: $--color-white;
        }
        &.active {
            background-color: $--color-white;
            color: $--color-text-secondary;
        }
	}
</style>
