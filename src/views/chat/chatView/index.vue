<template>
    <div ref="chat" class="hubble-chat">
        <div class="hubble-chat__header">
            <span>
                <img src="~img/AIBot.png" class="avatar" alt="">
                Hubble
            </span>
            <div class="operate">
                <el-popover
                    trigger="click"
                    popper-class="download-popover"
                    v-model="languageSwitchVisible"
                >
                    <!-- <el-tooltip slot="reference"
                        :open-delay="500"
                        effect="dark"
                        :content="$t('chat.switchOutputLanguage')"
                        placement="top"
                    >
                        <i class="el-icon-ssq-diqiu" id="guide-lang"></i>
                    </el-tooltip> -->
                    <ul>
                        <li :class="isEn ? '' : 'active'" @click="switchEnLanguage(false)">{{ $t('chat.chinese') }}</li>
                        <li :class="isEn ? 'active' : ''" @click="switchEnLanguage(true)">{{ $t('chat.english') }}</li>
                    </ul>
                </el-popover>
                <!-- <el-popover
                    trigger="click"
                    popper-class="download-popover"
                > -->
                <el-tooltip slot="reference"
                    :open-delay="500"
                    effect="dark"
                    :content="$t('chat.downloadFile')"
                    placement="top"
                >
                    <i @click="handleDownloadChat" class="el-icon-ssq--bs-xiazai"></i>
                </el-tooltip>
                <!-- <ul>
                        <li @click="handleDownloadChat(false)">{{ $t('chat.sourceFile') }}</li>
                        <li @click="handleDownloadChat">{{ $t('chat.sourceFileWithChat') }}</li>
                    </ul>
                </el-popover> -->
            </div>
        </div>
        <div class="hubble-chat__body" @scroll="handleScroll">
            <div class="hubble-chat__loading" v-if="chatInitLoading">
                <img src="~img/loading2.gif" alt=""><br>
                <span>{{ $t('chat.fileAnalyzing') }}</span>
            </div>
            <template v-else>
                <ChatList
                    :messages="messages"
                    @selectQuote="handleSelectQuote"
                    @showContinuousChat="handleShowContinuousChat"
                ></ChatList>
                <transition name="fade">
                    <div v-show="showContinuousChat" class="continuous-chat">
                        <ChatList
                            isContinuousChat
                            :messages="continuousMessages"
                            @selectQuote="handleSelectQuote"
                            @loadMore="handleLoadMoreContinuousChat"
                        ></ChatList>
                        <div class="continuous-exit">
                            <span>{{ $t('chat.continuousChat') }}</span>
                            <span class="exit" @click="showContinuousChat = false">{{ $t('common.exit') }}</span>
                        </div>
                    </div>
                </transition>
            </template>
        </div>
        <div class="hubble-chat__dialog">
            <transition name="slide">
                <div class="hubble-chat__quote" v-show="!!preQuote.content">
                    <div class="hubble-chat__quote-header">
                        <span>{{ $t('chat.quoteContent') }}</span>
                        <i class="el-icon-ssq-guanbi" @click="clearQuote"></i>
                    </div>
                    <div class="hubble-chat__quote-body chat-quote" @click="handleSelectQuote(preQuote)">{{ preQuote.content }}</div>
                </div>
            </transition>
            <transition name="slide">
                <div :class="`hubble-chat__suggestion ${!preQuote.content ? 'without-quote' : ''}`" v-show="showSuggestion">
                    <div class="hubble-chat__suggestion-header">
                        <i class="el-icon-ssq-gaoliangtishiH5"></i>
                        {{ $t('chat.suggestionsQuestions') }}:
                        <i class="el-icon-ssq-guanbi" @click="showSuggestion = false"></i>
                    </div>
                    <ul class="hubble-chat__suggestion-list">
                        <li
                            class="hubble-chat__suggestion-item"
                            v-for="(suggestion, i) in currentSuggestions"
                            :key="i"
                            @click="handleSendSuggestion(suggestion)"
                        >
                            <span>{{ suggestion.suggestQuestionContent }}</span>
                            <i class="el-icon-ssq-fajianxiang"></i>
                        </li>
                        <li class="hubble-chat__suggestion-item empty" v-if="!currentSuggestions.length">
                            <img src="~img/loading.gif" v-if="loadSuggestion" alt="">
                            <span v-else>{{ $t('tips.noData') }}</span>
                        </li>
                    </ul>
                </div>
            </transition>
        </div>
        <div class="hubble-chat__footer" v-if="!chatInitLoading">
            <div :class="`hubble-chat__input ${currentPrompt.displayName ? 'line-feed' : ''}`" @keydown="handleKeyDown">
                <ul class="hubble-chat__prompt" v-show="showPrompt">
                    <li
                        v-for="(plugin, index) in filterPromptList"
                        :class="`hubble-chat__prompt-plugin ${ index === currentPromptIndex ? 'active' : '' }`"
                        @click="handleSelectPlugin(plugin)"
                        :key="plugin.pluginName"
                    >
                        <i :class="plugin.icon" v-if="plugin.icon"></i>
                        <div>
                            <p>
                                {{ plugin.displayName }}
                                <span>（{{ plugin.shortName }}）</span>
                            </p>
                            <span>{{ plugin.desc }}</span>
                        </div>
                    </li>
                </ul>
                <span ref="inputPlugin" class="hubble-chat__input-plugin" v-show="!!currentPrompt.displayName">/ {{ currentPrompt.displayName }}</span>
                <el-input
                    id="guide-text"
                    ref="inputItem"
                    type="textarea"
                    :autosize="{ minRows: 1, maxRows: 6}"
                    resize="none"
                    :placeholder="$t('chat.inputQuestionTip')"
                    v-model="input"
                    :disabled="btnDisabled"
                    @blur="inputBlur"
                    @focus="inputActive = true"
                ></el-input>
            </div>
            <div class="hubble-chat__footer-operate">
                <el-tooltip :open-delay="500" effect="dark" :content="$t('chat.suggestionsQuestions')" placement="top">
                    <i id="guide-suggestion" class="operate-icon el-icon-ssq-Hubbletishi" @click="handleShowSuggestion"></i>
                </el-tooltip>
                <el-button type="primary" :disabled="btnDisabled" @click="handleSendMessage">{{
                    $t('common.send') }}</el-button>
            </div>
        </div>
        <i ref="move" class="hubble-chat__move el-icon-ssq-yidongbiaoqian"></i>
    </div>
</template>
<script>
import dayjs from 'dayjs';
import io from 'socket.io-client';
import { mapState, mapMutations, mapGetters } from 'vuex';
import ChatList from './chatList';
import { download } from 'utils/download.js';
export default {
    components: {
        ChatList,
    },
    props: {
        topicId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            chatInitLoading: true,
            messages: [],
            continuousMessages: [],
            showContinuousChat: false,
            startChatId: '',
            startContinuousChatId: '',
            multiTurnChatBeginId: '',
            loadChatSize: 20,
            noMore: false,
            noMoreContinuous: false,
            isLoadingMore: false,
            input: '',
            typing: false,
            showDialog: false,
            prepareTyping: false,
            typingInterval: null,
            hasInit: false,
            initTimeout: null,
            isEn: false,
            showSuggestion: false,
            loadSuggestion: false,
            suggestions: [],
            currentSuggestion: {},
            promptPlugins: [],
            inputActive: false,
            currentPrompt: {},
            currentPromptIndex: 0,
            languageSwitchVisible: false,
        };
    },
    computed: {
        ...mapGetters('hubble', ['documentId']),
        ...mapState('hubble', ['preQuote']),
        btnDisabled() {
            return this.typing || this.prepareTyping;
        },
        currentSuggestions() {
            return this.suggestions.filter(el => el.questionScope === (this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'));
        },
        filterPromptList() {
            if (this.input[0] !== '/') {
                return [];
            }
            const input = this.input.slice(1);
            const list = this.promptPlugins.filter(el => el.scope === (this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'));
            return input ? list.filter(el => this.containsAllChars(input, el.shortName) || this.containsAllChars(input, el.displayName)) : list;
        },
        showPrompt() {
            return !this.currentPrompt.shortName && this.inputActive && this.filterPromptList.length > 0;
        },
        currentMessages() {
            return this.showContinuousChat ? this.continuousMessages : this.messages;
        },
    },
    watch: {
        'preQuote.content': {
            handler(val) {
                if (val) {
                    this.currentPrompt = {};
                }
            },
        },
        'currentPrompt.shortName': {
            handler(val) {
                this.$nextTick(() => {
                    const input = document.querySelector('.hubble-chat__input').querySelector('.el-input__inner');
                    if (val) {
                        const width = this.$refs.inputPlugin.offsetWidth;
                        input.style.paddingLeft = `${width}px`;
                    } else {
                        input.style.paddingLeft = '10px';
                    }
                });
            },
        },
        messages: {
            handler() {
                this.$nextTick(() => {
                    this.scrollList();
                });
            },
            immediate: true,
            deep: true,
        },
        prepareTyping: {
            handler(val) {
                const message = this.currentMessages[0] || {};
                if (val) {
                    this.typingInterval = setInterval(() => {
                        if (message.answer.length === 3) {
                            message.answer = '.';
                        } else {
                            message.answer += '.';
                        }
                    }, 500);
                } else {
                    clearInterval(this.typingInterval);
                }
            },
            immediate: true,
        },
        filterPromptList() {
            this.currentPromptIndex = 0;
        },
    },
    methods: {
        ...mapMutations('hubble', ['setQuote']),
        switchEnLanguage(bool) {
            this.isEn = bool;
            this.languageSwitchVisible = false;
        },
        handleDownloadChat(withChat = true) {
            const downloadUrl = `/web-api/topic/${this.topicId}/documents/${this.documentId}/download${withChat ? '-with-quote-chats' : ''}`;
            download(downloadUrl);
        },
        handleShowContinuousChat(chatId) {
            this.continuousMessages = [];
            this.noMoreContinuous = false;
            this.multiTurnChatBeginId = chatId;
            this.startContinuousChatId = '';
            this.showContinuousChat = true;
            this.getContinuousChat();
        },
        getContinuousChat() {
            this.isLoadingMore = true;
            this.$http(`/web-api/topic/${this.topicId}/${this.multiTurnChatBeginId}/multi-turn-chat-history?startChatId=${this.startContinuousChatId}&number=${this.loadChatSize}`).then(({ data: { historyChats } }) => {
                const topicLength = historyChats.length;
                this.startContinuousChatId = topicLength ? historyChats[historyChats.length - 1].chatId : '';
                this.noMoreContinuous = topicLength < this.loadChatSize;
                this.continuousMessages = this.continuousMessages.concat(historyChats.map(el => {
                    return {
                        ...el,
                        chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                        quote: el.questionDocumentQuote?.content || '',
                    };
                }));
                // !topicLength && this.continuousMessages.push(this.messages.filter(el => el.chatId === this.multiTurnChatBeginId)[0]);
            }).finally(() => {
                this.isLoadingMore = false;
            });
        },
        handleLoadMoreContinuousChat(event) {
            if (this.noMoreContinuous || this.isLoadingMore) {
                return;
            }
            const list = event.target;
            if (list.scrollTop + list.scrollHeight === list.clientHeight) {
                this.getHistoryChats();
            }
        },
        handleKeyDown(event) {
            if (event.key === 'Enter') {
                if (this.showPrompt) {
                    event.preventDefault();
                    return this.handleSelectPlugin(this.filterPromptList[this.currentPromptIndex]);
                }
                !event.shiftKey && this.handleSendMessage();
            } else if (event.keyCode === 8 && !this.input && this.currentPrompt.displayName) {
                this.currentPrompt = {};
                setTimeout(() => {
                    this.input = '/';
                }, 50);
            } else if (event.key === 'ArrowDown') {
                if (this.showPrompt) {
                    this.currentPromptIndex = (this.currentPromptIndex + 1) % this.filterPromptList.length;
                }
            } else if (event.key === 'ArrowUp') {
                if (this.showPrompt) {
                    this.currentPromptIndex = (this.currentPromptIndex - 1 + this.filterPromptList.length) % this.filterPromptList.length;
                }
            }
        },
        handleSelectPlugin(plugin) {
            this.currentPrompt = plugin;
            this.input = '';
        },
        inputBlur() {
            setTimeout(() => {
                this.inputActive = false;
            }, 100);
        },
        containsAllChars(str, target) {
            const regex = new RegExp([...str.toLowerCase()].map(c => `${c}.*`).join(''));
            return regex.test(target.toLowerCase());
        },
        scrollList() {
            const element = document.querySelector('.hubble-chat__body');
            element.scrollTop = element.scrollHeight;
        },
        clearQuote() {
            this.setQuote({});
        },
        async handleSendMessage() {
            if (this.input.trim()) {
                const messages = this.currentMessages;
                messages.unshift({
                    question: this.input.trim(),
                    questionDocumentQuote: this.preQuote,
                    chatTime: new Date().toLocaleString(),
                    quote: this.preQuote.content,
                    answer: '',
                });
                this.sendMessage();
                this.input = '';
            }
        },
        handleScopeAndPlugin() {
            if (this.currentPrompt.pluginName) {
                return {
                    questionScope: this.currentPrompt.scope,
                    enablePlugin: this.currentPrompt.pluginName,
                };
            } else if (this.currentSuggestion.pluginName) {
                return {
                    questionScope: this.currentSuggestion.questionScope,
                    enablePlugin: this.currentSuggestion.pluginName,
                };
            }
            return {
                questionScope: this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT',
                enablePlugin: this.preQuote.content ? 'FREE_CHAT_QUOTE' : 'FREE_CHAT_DOCUMENT',
            };
        },
        sendMessage() {
            const scopeAndPlugin = this.handleScopeAndPlugin();
            this.currentPrompt = {};
            this.currentSuggestion = {};
            this.prepareTyping = true;
            setTimeout(() => {
                this.clearQuote();
            }, 50);
            // 监听连接成功事件
            // this.socket.emit('ask-pdf-event', {
            //     ...scopeAndPlugin,
            //     question: this.input.trim(),
            //     questionDocumentQuote: this.preQuote,
            //     chatLanguage: this.isEn ? 'ENGLISH' : 'CHINESE',
            //     multiTurnChatBeginId: this.multiTurnChatBeginId,
            //     ifMultiTurnChat: this.showContinuousChat,
            // })
            // return;
            return this.$http.post(`/web-api/topic/${this.topicId}/ask-document`, {
                ...scopeAndPlugin,
                question: this.input.trim(),
                questionDocumentQuote: this.preQuote,
                chatLanguage: this.isEn ? 'ENGLISH' : 'CHINESE',
                multiTurnChatBeginId: this.multiTurnChatBeginId,
                ifMultiTurnChat: this.showContinuousChat,
            }).then(res => {
                this.$nextTick(() => {
                    const answer = res.data.answer || this.$t('tips.systemOcupied');
                    const messages = this.currentMessages;
                    const message = messages[0];
                    this.$nextTick(() => {
                        message.sendContent = answer;
                        this.animateMessage().then(() => {
                            this.$set(messages, 0, {
                                ...messages[0],
                                ...res.data,
                                chatTime: dayjs(res.data.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                                quote: res.data.questionDocumentQuote?.content || '',
                            });
                        });
                    });
                });
            }).catch(err => {
                const message = this.currentMessages[0];
                this.$nextTick(() => {
                    message.sendContent = err.response?.data.message || this.$t('tips.systemOcupied');
                    this.animateMessage();
                });
            }).finally(() => {
                this.prepareTyping = false;
            });
        },
        animateMessage() {
            this.typing = true;
            const message = this.currentMessages[0];
            const content = message.sendContent;
            let i = 0;
            return new Promise(resolve => {
                const interval = setInterval(() => {
                    message.answer = content.slice(0, i + 1);
                    this.scrollList();
                    i++;
                    if (i === content.length) {
                        clearInterval(interval);
                        this.typing = false;
                        resolve();
                    }
                }, 50);
            });
        },
        initSuggestions() {
            this.loadSuggestion = true;
            this.$http(`/web-api/topic/${this.topicId}/suggest-questions`).then(res => {
                this.suggestions = res.data.suggestQuestions;
            }).finally(() => {
                this.loadSuggestion = false;
            });
        },
        handleSendSuggestion(suggestion) {
            this.input = suggestion.suggestQuestionContent;
            this.currentSuggestion = suggestion;
            this.showSuggestion = false;
            this.handleSendMessage();
        },
        handleSelectQuote(quote) {
            const { quoteCoordinate, pageNumber } = quote;
            this.$store.state.hubble.quotePage = pageNumber;
            this.$store.state.hubble.quoteCoordinate = quoteCoordinate;
            this.$store.state.hubble.startSelectQuote = true;
        },
        initMove() {
            const chatHandle = this.$refs.move;
            const chat = this.$refs.chat;
            let isDragging = false;
            let lastX;

            chatHandle.addEventListener('mousedown', (e) => {
                isDragging = true;
                lastX = e.clientX;
                document.body.style.userSelect = 'none';
            });
            document.addEventListener('mousemove', (e) => {
                const chatWidth = chat.offsetWidth;
                const delta = lastX - e.clientX;
                if (!isDragging || (chatWidth <= 450 && delta < 0)) {
                    return;
                }
                chat.style.width = `${chatWidth + delta}px`;
                lastX = e.clientX;
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
                document.body.style.userSelect = 'unset';
            });
        },
        initPrompt() {
            const iconMap = {
                contractDetail: 'el-icon-ssq-Hubblehetongxiangqing',
                contractContent: 'el-icon-ssq-Hubblehetongneirong',
            };
            this.$http(`/web-api/topic/${this.topicId}/chat-plugins`).then(res => {
                this.promptPlugins = res.data.plugins.map(el => {
                    return {
                        ...el,
                        icon: iconMap[el.shortName],
                    };
                });
            });
        },
        waitChatInit() {
            return new Promise((resolve, reject) => {
                this.$http(`/web-api/topic/${this.topicId}/if-inited`).then(res => {
                    if (res.data.ifInitSuccess) {
                        this.chatInitLoading = false;
                        resolve();
                    } else {
                        this.initTimeout = setTimeout(() => {
                            this.waitChatInit().then(resolve);
                        }, 3000);
                    }
                }).catch(() => {
                    reject();
                });
            });
        },
        async handleScroll(event) {
            if (this.noMore || this.isLoadingMore) {
                return;
            }
            const list = event.target;
            if (list.scrollTop + list.scrollHeight === list.clientHeight) {
                this.getHistoryChats();
            }
        },
        getHistoryChats() {
            this.isLoadingMore = true;
            this.$http(`/web-api/topic/${this.topicId}/history-chats?startChatId=${this.startChatId}&number=${this.loadChatSize}`).then(({ data: { historyChats } }) => {
                const topicLength = historyChats.length;
                this.startChatId = topicLength ? historyChats[historyChats.length - 1].chatId : '';
                this.noMore = topicLength < this.loadChatSize;
                this.messages = this.messages.concat(historyChats.map(el => {
                    return {
                        ...el,
                        chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),
                        quote: el.questionDocumentQuote?.content || '',
                    };
                }));
            }).finally(() => {
                this.isLoadingMore = false;
            });
        },
        initMessages() {
            this.startChatId = '';
            this.messages = [];
            this.getHistoryChats();
        },
        async init() {
            // this.initSocket();
            await this.waitChatInit();
            this.initMove();
            this.initPrompt();
            this.initSuggestions();
            await this.initMessages();
        },
        handleShowSuggestion() {
            // this.showSuggestion = !this.showSuggestion;
            if (this.showSuggestion) {
                return (this.showSuggestion = false);
            }
            this.suggestions = [];
            this.loadSuggestion = true;
            this.showSuggestion = true;
            this.socket.emit('suggest-question-event', {
                quoteContent: this.preQuote.content, // 非必填，引用内容
                leastMultiTurnChatId: this.showContinuousChat ? this.continuousMessages[this.continuousMessages.length - 1]?.chatId || this.multiTurnChatBeginId : '', // 非必填， 最近的连续对话编号
                questionScope: this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT', // 必填，QUOTE:引用, FULL_DOCUMENT:全文
                chatType: this.showContinuousChat ? 'MULTI_TURN_CHAT' : 'CHAT', // 必填，CHAT:非连续对话, MULTI_TURN_CHAT:连续对话
            });
        },
        initSocket() {
            this.socket = io('/web-hubble/ask-pdf', {
                path: '/web-hubble',
                reconnectionDelay: 5000,
                query: {
                    topicId: this.topicId,
                },
                transports: ['polling', 'websocket'],
                extraHeaders: {
                    Authorization: 'bearer ' + this.$cookie.get('access_token'),
                },
            });
            // 监听连接成功事件
            this.socket.on('connect', () => {
                console.log('连接成功');
            });
            this.socket.on('connect_error', (error) => {
                console.log('连接失败', error);
            });
            // this.socket.emit('ask-pdf-event', {});
            // const _this = this;
            // this.socket.on('ask-pdf-event', (data) => {
            //     console.log(data);
            //     _this.prepareTyping = false;
            //     _this.$nextTick(() => {
            //         const answer = data;
            //         const message = _this.currentMessages[0];
            //         _this.$nextTick(() => {
            //             message.answer += answer;
            //         });
            //     });
            // });

            this.socket.on('suggest-question-event', (data) => {
                this.loadSuggestion = false;
                this.suggestions = data.suggestQuestions;
            });
        },
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
        this.clearQuote();
        this.initTimeout && clearTimeout(this.initTimeout);
    },
};
</script>

<style lang="scss">
@import './index.scss'
</style>
