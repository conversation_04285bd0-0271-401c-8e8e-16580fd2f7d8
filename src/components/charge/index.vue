<template>
    <div>
        <el-dialog
            class="hubble-contract-compare"
            :visible="show"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleClose"
        >
            <div slot="title" class="hubble-contract-compare_header">
                <span class="title">{{ purchaseTitle }}</span>
                <i class="el-icon-close" @click="handleClose"></i>
            </div>
            <div class="hubble-contract-compare_content">
                <div class="package-content">
                    <div class="package-item" v-for="item in chargeList" :key="item.productPackageId">
                        <span class="item-first-purchase" v-if="item.isFirstPurchase">{{ $t('contractCompare.payOnce') }}</span>
                        <p class="item-name">{{ item.name }}</p>
                        <p class="item-price">¥{{ item.price }}<span v-if="item.discount >0" class="item-discount"><i class="el-icon-ssq-xiajiang1"></i>{{ item.discount }}%&nbsp;OFF</span></p>
                        <p class="item-origin-price">¥{{ item.originPrice }}</p>
                        <el-button type="primary" @click="pay(item)">{{ $t('contractCompare.payNow') }}</el-button>
                        <ul class="item-content">
                            <li>{{ getProductConfig(item.productType).title + $t('hubblePackage.amount') }}：<span>{{ item.contractNum + getProductConfig(item.productType).unitPer }}</span></li>
                            <li>{{ getProductConfig(item.productType).title + $t('hubblePackage.unitPrice') }}：<span>￥{{ (item.price/item.contractNum).toFixed(2) }}/{{ getProductConfig(item.productType).unitPer }}</span></li>
                            <li>{{ $t('contractCompare.limitTime') }}：<span>{{ item.validityMonth }}{{ $t('contractCompare.month') }}</span></li>
                        </ul>
                    </div>
                </div>
                <div class="package-info">
                    <p class="p1">{{ $t('contractCompare.compareInfo1') }}</p>
                    <p v-if="checkType()">{{ $t('contractCompare.compareInfo2', { type: getProduct.title ,per:getProduct.per, index: checkType() ? '1' : '0'}) }}</p>
                    <p>{{ $t(`contractCompare.compareInfo3`, {per:getProduct.per, index: checkType() ? '2' : '1'}) }}</p>
                </div>
                <div class="package-info">
                    <p class="p1">{{ $tc('contractCompare.contactUs', 1) }}</p>
                    <p class="p2">{{ $tc('contractCompare.contactUs', 2) }}</p>
                    <!-- <img src="~img/contractHubble/ssq-qrcode.png" alt=""> -->
                </div>
            </div>
        </el-dialog>
        <el-dialog
            class="hubble-contract-pay"
            :visible="showPay"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleClosePay"
        >
            <div slot="title" class="hubble-contract-compare_header">
                <span class="title">{{ isIcbcDeveloper ? '工商银行企业网银' : $t('contractCompare.codePay') }}</span>
                <i class="el-icon-close" @click="handleClosePay"></i>
            </div>
            <div class="pay-content">
                <div class="pay-content-tab">
                    <div
                        :class="activePay === item.type ? 'activated' : ''"
                        v-for="item in payMethods"
                        :key="item.type"
                        @click="changePay(item.type)"
                    >{{ item.name }}</div>
                </div>
                <div class="pay-info">
                    <div class="pay-info-code" :class="activePay" v-if="!isIcbcDeveloper">
                        <!-- 支付宝二维码是iframe -->
                        <div class="qrcode-img-con-ali" v-if="isAlipay" v-loading="imgScrLoading">
                            <iframe class="qrcode-img-ali"
                                :src="payQrCode"
                                ref="aliIframe"
                                frameborder="0"
                            ></iframe>
                        </div>
                        <!-- 微信支付二维码是图片 -->
                        <div class="qrcode-img-con-wechat" v-else v-loading="imgScrLoading">
                            <img class="qrcode-img" :src="payQrCode" v-if="!imgScrLoading" alt="pay qrcode">
                        </div>
                    </div>
                    <div class="pay-info-content">
                        <div><span>{{ $tc('contractCompare.payIno', 0) }}：</span>{{ operatePackage.name }}</div>
                        <div><span>{{ $tc('contractCompare.payIno', 1) }}：</span>{{ isPersonType ? getUserAccount : getCurrentEntInfo.entName }}</div>
                        <div><span>￥{{ $tc('contractCompare.payIno', 2) }}：</span>{{ operatePackage.price }}</div>
                        <div>
                            <el-button type="primary" @click="toIcbcPay">去工行支付</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
// import { getChargePackage, getChargeOrder, checkPay, getWeiXinPayImg, getAliPayImg } from 'src/api/comparison.js';

export default {
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        productType: {
            type: [Number, Array],
            default: () => 15, // 15比对，16翻译
        },
        productConfig: {
            type: Object,
            default: () => {},
        },
        onlyPerson: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            allChargeList: [],
            chargeList: [],
            showPay: false,
            activePay: 'aliPay',
            operatePackage: {},
            weiXinPayImg: '', // 微信支付img
            aliPayImg: '', // 支付宝支付img
            interval: null, // 定时器
            weixinImgSrcLoading: false, // 微信支付二维码loading
            aliImgSrcLoading: false, // 支付宝支付二维码loading
            hasPay: false,
            getUserAccount: '',
        };
    },
    computed: {
        ...mapGetters(['isPerson', 'getCurrentEntInfo', 'getIsForeignVersion']),
        isPersonType() {
            return this.onlyPerson || this.isPerson;
        },
        isIcbcDeveloper() {
            return false;
        },
        payMethods() {
            return this.isIcbcDeveloper ? [{ name: '工商银行支付', type: 'icbc' }] : [
                {
                    name: this.$t('contractCompare.aliPay'),
                    type: 'aliPay',
                },
                {
                    name: this.$t('contractCompare.wxPay'),
                    type: 'wxPay',
                },
            ];
        },
        isAlipay() {
            return this.activePay === 'aliPay';
        },
        // 支付二维码
        payQrCode() {
            return  this.isAlipay ? this.aliPayImg : this.weiXinPayImg;
        },
        // 支付二维码loading
        imgScrLoading() {
            return  this.isAlipay ?  this.aliImgSrcLoading : this.weixinImgSrcLoading;
        },
        getProduct() {
            if (this.productConfig.title) {
                return this.productConfig;
            } else {
                const firstType = Object.keys(this.productConfig)[0];
                const productConfig = this.productConfig[firstType];
                return productConfig;
            }
        },
        getProductConfig() {
            return (type) => {
                if (this.productConfig[type]) {
                    return this.productConfig[type];
                } else {
                    return this.productConfig;
                }
            };
        },
        purchaseTitle() {
            let title = '';
            const type = Object.prototype.toString.call(this.productType);
            if (type === '[object Number]') {
                title = this.$t('contractCompare.packagePurchaseTitle', { title: this.getProduct.title });
            } else {
                title = this.$t('contractCompare.packagePurchase');
            }
            return title;
        },
    },
    watch: {
        show(val) {
            if (val) {
                const type = Object.prototype.toString.call(this.productType);
                if (type === '[object Number]') {
                    this.chargeList = this.allChargeList.filter((item) => item.productType === this.productType);
                } else if (type === '[object Array]') {
                    this.chargeList = this.allChargeList.filter((item) => this.productType.includes(item.productType));
                }
                this.chargeList.forEach(item => {
                    item.discount = ((1 - item.price / item.originPrice) * 100).toFixed(0);
                });
            }
        },
    },
    methods: {
        toIcbcPay() {
            window.open('https://corporbank.icbc.com.cn/icbc/corporbank/epassclogon.jsp?ItemNo=E9638');
        },
        handleIcbcPay() {
            return this.$http.get(`/ents/icbc/request/${this.orderInfo.orderId}`);
        },
        handleClose() {
            this.$emit('handleClose', this.hasPay);
        },
        handleClosePay() {
            this.interval && clearInterval(this.interval);
            this.aliPayImg = '';
            this.activePay = 'aliPay';
            this.showPay = false;
            this.$emit('handleClose', this.hasPay);
        },
        pay(item) {
            const loading = this.$loading();
            this.operatePackage = item;
            this.$emit('hideDialog');
            // 模拟获取订单
            this.orderInfo = { orderId: 'mock-order-id' };
            if (this.getIsForeignVersion) {
                this.checkPayResult();
                window.open(`${window.location.origin}/ents/paypal/request/${this.orderInfo.orderId}`);
                loading.close();
                return;
            }
            this.showPay = true;
            if (this.activePay === 'icbc') {
                this.handleIcbcPay();
                this.checkPayResult();
            } else {
                this.getAlipayQrCode();
            }
            loading.close();
        },
        changePay(type) {
            this.activePay = type;
            if (this.activePay === 'aliPay') {
                this.aliPayImg = '';
                this.getAlipayQrCode();
            } else {
                this.getWeChatPayQrCode();
            }
        },
        // 获取支付宝支付img
        getAlipayQrCode() {
            this.aliImgSrcLoading = true;
            // 模拟获取支付宝二维码
            setTimeout(() => {
                this.aliPayImg = 'data:text/html;charset=utf-8,<div style="text-align:center;padding:20px;">支付宝支付二维码</div>';
                this.iframeLoad();
                this.checkPayResult();
            }, 1000);
        },
        // 获取微信支付img
        getWeChatPayQrCode() {
            this.weixinImgSrcLoading = true;
            // 模拟获取微信支付二维码
            setTimeout(() => {
                this.weixinImgSrcLoading = false;
                this.weiXinPayImg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwOTk0NCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5b6u5L+h5LqM57u05LiA56CB</text></svg>';
                this.checkPayResult();
            }, 1000);
        },
        // 支付宝二维码loading监听
        iframeLoad() {
            this.aliImgSrcLoading = true;
            const iframe = this.$refs.aliIframe;
            if (iframe) {
                // 兼容处理
                if (iframe.attachEvent) {
                    // IE
                    iframe.attachEvent('onload', () => {
                        this.aliImgSrcLoading = false;
                    });
                } else {
                    // 非IE
                    iframe.onload = () => {
                        this.aliImgSrcLoading = false;
                    };
                }
            } else {
                this.aliImgSrcLoading = false;
            }
        },
        // 轮询检查支付结果
        checkPayResult() {
            this.interval && clearInterval(this.interval);
            let times = 180;
            this.interval = setInterval(() => {
                times--;
                // 模拟支付检查
                const mockPaySuccess = Math.random() > 0.95; // 5%概率支付成功
                if (mockPaySuccess) {
                    this.$MessageToast.success(this.$t('contractCompare.paySuccess'));
                    this.hasPay = true;
                    this.handleClosePay();
                } else if (times <= 0) {
                    this.hasPay = false;
                    clearInterval(this.interval);
                }
            }, 2000);
        },
        checkType() {
            const type = Object.prototype.toString.call(this.productType);
            if (type === '[object Number]') {
                return ![22, 27].includes(this.productType);
            } else if (type === '[object Array]') {
                return !this.productType.includes(22) && !this.productType.includes(27);
            }
        },
    },
    created() {
        // 模拟获取套餐列表
        this.allChargeList = [
            {
                productPackageId: 1,
                name: '基础套餐',
                price: 99,
                originPrice: 199,
                contractNum: 10,
                validityMonth: 12,
                productType: 15,
                isFirstPurchase: true,
            },
            {
                productPackageId: 2,
                name: '专业套餐',
                price: 299,
                originPrice: 599,
                contractNum: 50,
                validityMonth: 12,
                productType: 15,
                isFirstPurchase: false,
            },
        ];
        this.isIcbcDeveloper && (this.activePay = 'icbc');
    },
    beforeDestroy() {
        this.interval && clearInterval(this.interval);
    },
};
</script>

<style lang="scss" scoped>
    $--color-text-primary: #333333;
    $--color-text-second: #999999;
    $--color-text-third: #666666;
    $--color-border: #EEEEEE;
    $--color-white: #FFFFFF;
    $--color-black: #000000;
    $--color-bg: #F8F8F8;

    .hubble-contract-compare, .hubble-contract-pay {
        z-index: 99998 !important;
        *{
            box-sizing: border-box;
        }
        &::v-deep .el-dialog--small {
            width: 920px;
            height: 600px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        &::v-deep .el-dialog__header {
            flex-shrink: 0;
            padding: 0;
            border-bottom: 1px solid $--color-border;
            .hubble-contract-compare_header {
                height: 50px;
                padding: 0 20px 0 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .title {
                    font-size: 16px;
                    color: $--color-text-primary;
                }
                .el-icon-close {
                    cursor: pointer;
                    font-size: 12px;
                    color: $--color-text-second;
                }
            }
            .el-dialog__headerbtn {
                display: none;
            }
        }
        &::v-deep .el-dialog__body {
            padding: 20px 30px;
            flex-grow: 1;
            overflow: auto;
        }
        .package-content {
            display: flex;
            flex-wrap: wrap;
            align-items: stretch;
        }
        .package-item {
            display: inline-block;
            vertical-align: middle;
            width: 200px;
            // height: 280px;
            padding: 25px 10px;
            margin-top: 0;
            margin-right: 15px;
            margin-bottom: 20px;
            border-radius: 6px;
            background: #F5F6FA;
            position: relative;
            [dir="rtl"] & {
                margin-right: 0px;
                margin-left: 15px;
                padding: 25px 20px 25px 0px;
            }
            &:nth-child(4n){
                margin-right: 0;
                [dir="rtl"] & {
                    margin-left: 0px;
                }
            }
            .item-first-purchase{
                border-radius: 0px 6px 0px 9px;
                display: inline-block;
                background: #FF7A6C;
                position: absolute;
                top: 0;
                right: 0;
                color: #fff;
                font-size: 10px;
                padding: 2px 4px 2px 6px;
            }
            .item-name{
                font-size: 16px;
                // font-weight: 500;
                color: $--color-text-primary;
            }
            .item-price{
                font-size: 24px;
                color: rgba(18, 127, 210, 1);
                margin-top:18px;
                line-height: 28px;
                .item-discount{
                    border-radius: 2px;
                    background: #E6F4FF;
                    border: 1px solid #127FD2;
                    font-size: 12px;
                    vertical-align: text-bottom;
                    padding: 1px 4px;
                    margin-left: 4px;
                    i{margin-right: 2px;}
                }
            }
            .item-origin-price{
                font-size: 12px;
                text-decoration-line: line-through;
                color: #96a2b5;
                line-height: 20px;
            }

            .item-content{
                font-size: 12px;
                color: #96a2b5;
                line-height: 20px;
                margin-left: 14px;
                li{
                    position: relative;
                    &::before{
                        position: absolute;
                        content: '';
                        top: 8px;
                        left: -8px;
                        display: inline;
                        width: 4px;
                        height: 4px;
                        border-radius: 4px;
                        background: #96a2b5;
                        z-index: 1;
                    }
                    span{
                        color: $--color-text-primary;
                    }
                }
            }
            .el-button {
                margin: 20px auto 24px;
                width: 160px;
                height: 36px;
                background-color:#0C8AEE;
                border-radius: 6px;
                position: relative;
            }
        }
        .package-info {
            margin-top: 8px;
            color: $--color-text-second;
            font-size: 12px;
            p {
                line-height: 20px;
            }
            .p1{
                font-size: 14px;
                color: #43526C;
            }
            img{
                width: 100px;
                height: 100px;
                border: 1px solid #E5E5E5;
                padding: 6px;
                border-radius: 3px;
                margin-top: 10px;
            }
        }
    }
    .hubble-contract-pay {
        &::v-deep .el-dialog--small {
            width: 600px;
        }
        &::v-deep .el-dialog__body {
            padding: 0;
            padding-bottom: 46px;
            .pay-content-tab {
                display: flex;
                background-color: $--color-bg;
                div {
                    margin-left: 30px;
                    height: 42px;
                    border-bottom: 2px solid transparent;
                    cursor: pointer;
                    line-height: 42px;
                    text-align: center;
                }
                .activated {
                    border-bottom-color: #0C8AEE;
                    color: #0C8AEE;
                }
            }
            .pay-info {
                display: flex;
                padding: 30px 0 0 30px;
                .aliPay {
                    background: url('~@/assets/images/contractHubble/aliPay-bg.png') no-repeat;
                }
                .wxPay {
                    background: url('~@/assets/images/contractHubble/wxPay-bg.png') no-repeat;
                }
                .pay-info-code {
                    position: relative;
                    width: 130px;
                    height: 170px;
                    background-size: contain;
                    .qrcode-img-con-ali{
                        position: absolute;
                        width: 102px;
                        height: 104px;
                        left: 15px;
                        top: 15px;
                        background-color: white;
                        iframe {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .qrcode-img-con-wechat{
                        position: absolute;
                        width: 105px;
                        height: 105px;
                        left: 13px;
                        top: 13px;
                        img {
                            width: 100%;
                        }
                    }
                }
                .pay-info-content {
                    padding-left: 30px;
                    div {
                        color: $--color-text-primary;
                        margin-bottom: 12px;
                        span {
                            font-weight: 500;
                        }
                    }
                    .el-button {
                        height: 30px;
                        margin-top: 22px;
                        line-height: 9px;
                    }
                }
            }
        }
    }
</style>
