<template>
    <el-dialog
        title="我的套餐"
        :visible.sync="overviewDialog"
        width="60%"
        class="hubble-workspace__dialog"
        :before-close="closeDialog"
    >
        <div class="hubble-workspace__dialog-body">
            <div class="hubble-workspace__dialog-body-left">
                <div class="hubble-workspace__dialog-body-left-tab">
                    <img src="img/pie.png" alt="">
                    <div class="hubble-workspace__dialog-body-left-tab-title">套餐详情</div>
                </div>
            </div>
            <div class="hubble-workspace__dialog-body-right">
                <p class="hubble-workspace__dialog-body-right-total">剩余总页数：{{ getTotalPage(overview) }}页</p>
                <div class="hubble-workspace__dialog-body-right-detail" v-for="item in overview" :key="item.toolPlanId">
                    <div class="hubble-workspace__dialog-body-right-detail-percent">
                        <p class="hubble-workspace__dialog-body-right-detail-percent-name">{{ item.toolProductName }}:</p>
                        <el-progress :percentage="getPercent(item)" :show-text="false"></el-progress>
                        <div class="hubble-workspace__dialog-body-right-detail-percent-desc">
                            <span>已用{{ item.productOfferingCount - item.productBalanceCount }}页</span>
                            <p>可用剩余{{ item.productBalanceCount }}页 / 共{{ item.productOfferingCount }}页</p>
                        </div>
                    </div>
                    <p class="hubble-workspace__dialog-body-right-detail-expire">
                        到期时间：{{ moment(item.expireTime).format('YYYY-MM-DD HH:mm:ss') }}
                    </p>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import moment from 'dayjs';
export default {
    props: {
        overviewDialog: {
            type: Boolean,
            default: false,
        },
        workspaceId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            overview: [],
        };
    },
    computed: {
        getPercent() {
            return (item) => {
                const percent = (100 * (item.productOfferingCount - item.productBalanceCount) / item.productOfferingCount).toFixed(2) - 0;
                return percent;
            };
        },
        getTotalPage() {
            return (list) => {
                const total = list.reduce((pre, cur) => {
                    return pre + cur.productBalanceCount;
                }, 0);
                return total;
            };
        },
    },
    methods: {
        moment,
        overviewPlanDetail() {
            this.$http.get(
                `/web-api/users/tool/overview-plan-detail/${this.workspaceId}?toolType=合同内容抽取`,
            ).then((res) => {
                this.overview = res.data;
            });
        },
        closeDialog() {
            this.$emit('closeDialog');
        },
    },
    created() {
        this.overviewPlanDetail();
    },
};
</script>

<style lang="scss" scoped>
.hubble-workspace__dialog-body {
    display: flex;
    &-left {
        padding: 10px;
        background-color: #f8f8f8;
        flex-shrink: 0;
        &-tab {
            padding: 10px 20px;
            background-color: #fff;
            display: flex;
            align-items: center;
            font-size: 14px;
            border-radius: 4px;
            img {
                display: block;
                height: 14px;
                margin-right: 10px;
            }
        }
    }
    &-right {
        padding: 10px 20px;
        background-color: #fff;
        flex-grow: 1;
        &-total {
            font-size: 14px;
            margin-bottom: 10px;
        }
        &-detail {
            margin-bottom: 10px;
            background-color: #fff;
            overflow: hidden;
            &-percent {
                padding: 20px;
                background-color: #f8f8f8;
                border-radius: 4px;
                margin-bottom: 10px;
                &-name {
                    font-size: 13px;
                    margin-bottom: 10px;
                }
                &-desc {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-size: 12px;
                    margin-top: 10px;
                    span {
                        color: #000;
                    }
                    p {
                        color: #999;;
                    }
                }
            }
            &-expire {
                text-align: right;
                font-size: 12px;
                color: #999;;
            }
        }
    }
}
</style>
