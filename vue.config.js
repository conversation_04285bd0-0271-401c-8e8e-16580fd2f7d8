'use strict';
const fs = require('fs');
const path = require('path');
const webpack = require('webpack');
const NODE_ENV = process.env.NODE_ENV;
// const ProgressBarPlugin = require('progress-bar-webpack-plugin');
// const HardSourceWebpackPlugin = require('hard-source-webpack-plugin');
const SentryWebpackPlugin = require('@sentry/webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');

// 是否是development模式
const isDevelopment = NODE_ENV.indexOf('development') > -1;
const releaseNum = 'ent@' + new Date().getFullYear() + '_' + (new Date().getMonth() + 1);
const ignoreSentry = process.env.VUE_APP_NO_SENTRY === 'ignore-sentry';
function sentrySourceMapPlugin() {
    const plugins = [];
    if (!ignoreSentry && !isDevelopment) {
        plugins.push(new CleanWebpackPlugin());
        plugins.push(new SentryWebpackPlugin({
            release: releaseNum,
            include: './dist',
            ignore: ['node_modules', 'vue.config.js'],
            configFile: 'sentry.properties',
        }));
    }
    return plugins;
}
function resolve(dir) {
    return path.join(__dirname, dir);
}

const prodPlugins = [];
if (!isDevelopment) {
    // prodPlugins.push(new ProgressBarPlugin());
    // prodPlugins.push(new HardSourceWebpackPlugin());
}
// 将proxy.config.dev.js文件中的配置格式化成vue-cli可以接受的格式
function proxyFormat() {
    const proxyFile = fs.readFileSync(resolve('proxy.config.dev.js'), 'utf-8');
    // eslint-disable-next-line
    const proxyConfig = eval(proxyFile);
    return proxyConfig.reduce((proxy, item) => {
        const context = item.context;
        delete item.context;
        if (typeof context === 'string') {
            proxy[context] = item;
        } else {
            context.forEach((c) => {
                proxy[c] = item;
            });
        }
        return proxy;
    }, {});
}

module.exports = {
    publicPath: isDevelopment ? '/' : '/web',
    assetsDir: isDevelopment ? 'hubble' : '',
    outputDir: 'dist',
    lintOnSave: false,
    productionSourceMap: true,
    devServer: {
        host: '0.0.0.0',
        historyApiFallback: true,
        overlay: {
            warnings: true,
            errors: true,
        },
        hot: true,
        disableHostCheck: true,
        clientLogLevel: 'silent',
        open: true,
        proxy: (() => {
            const proxy = proxyFormat();
            Object.keys(proxy).forEach(item => {
                proxy[item].router = function(request) {
                    const proxy = proxyFormat();
                    const item = Object.keys(proxy).find(key => request.url.indexOf(key) > -1);
                    return (item && proxy[item].target) || false;
                };
            });
            return proxy;
        })(),
    },
    configureWebpack: {
        resolve: {
            extensions: ['*', '.js', '.vue', '.json', '.css', '.scss', '.less'],
            alias: {
                'src': resolve('src'),
                'http': resolve('src/http'),
                'styles': resolve('src/styles'),
                'lang': resolve('src/lang'),
                'const': resolve('src/const'),
                'img': resolve('src/assets/images'),
                'plugins': resolve('src/plugins'),
                'utils': resolve('src/utils'),
                'mixins': resolve('src/mixins'),
                'components': resolve('src/components'),
                'views': resolve('src/views/'),
                'iconfont': resolve('src/assets/iconfont'),
            },
        },
        devtool: isDevelopment ? 'eval-cheap-module-source-map' : undefined,
        // plugins: sentrySourceMapPlugin(),
    },
    chainWebpack(config) {
        config.plugins.delete('preload');
        config.plugins.delete('prefetch');

        // CopyPlugin
        config.plugin('copy')
            .tap((args) => {
                let copyObj = [];
                // 生产环境
                if (!isDevelopment) {
                    copyObj = [
                        ...copyObj,
                        {
                            from: 'yiL2SvxliG.txt',
                            to: 'yiL2SvxliG.txt',
                        },
                        { /* 这个名字不能改哦！！这个文件是为了配置小程序的业务域名，如果改了的话，小程序里面则打不开我们的h5页面了，勿动！！ */
                            from: 'WW_verify_OXYYVH4IowSYaNvw.txt',
                            to: 'WW_verify_OXYYVH4IowSYaNvw.txt',
                        },
                    ];
                }
                return [args[0].concat(copyObj)];
            });

        // ProvidePlugin
        config.plugin('provide')
            .use(webpack.ProvidePlugin, [{
                Vue: ['vue/dist/vue.runtime.esm.js', 'default'],
                VueRouter: 'vue-router/dist/vue-router.min.js',
                Vuex: 'vuex/dist/vuex.min.js',
            }]);

        // DefinePlugin
        config.plugin('define')
            .tap((args) => {
                return [{
                    ...args[0],
                    pdfCmaps: JSON.stringify('/public_static/cmaps/'),
                }];
            });

        config.module
            .rule('vue')
            .use('vue-loader')
            .loader('vue-loader')
            .tap(options => {
                options.compilerOptions.preserveWhitespace = true;
                return options;
            })
            .end();

        config
            .when(!isDevelopment,
                config => {
                    config
                        .plugin('ScriptExtHtmlWebpackPlugin')
                        .after('html')
                        .use('script-ext-html-webpack-plugin', [{
                            inline: /runtime\..*\.js$/,
                        }])
                        .end();
                    config.optimization.runtimeChunk('single');
                },
            );

        // 开发环境不建议使用此模式，热更新比较耗时，但旗舰版遗留问题，暂时先这样处理
        config.optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
                vendors: {
                    name: 'chunk-vendors',
                    test: /[\\/]node_modules[\\/]/,
                    priority: -10,
                    chunks: 'initial',
                },
                common: {
                    name: 'chunk-common',
                    minChunks: 2,
                    priority: -20,
                    chunks: 'initial',
                    reuseExistingChunk: true,
                },
                // css代码如果根据动态导入分割css文件，会有样式问题，暂时先将所有css合并成一个包
                styles: {
                    name: 'styles',
                    test: module => module.constructor.name === 'CssModule',
                    chunks: 'all',
                    enforce: true,
                    minChunks: 1,
                    priority: -15,
                },
            },
        });
    },
    // 预定义变量
    css: {
        loaderOptions: {
            scss: {
                prependData: `@import '~styles/variables.scss';`,
            },
        },
        extract: { // 开发环境不建议使用此模式
            ignoreOrder: true,
        },
    },
    pluginOptions: {
        webpackBundleAnalyzer: {
            analyzerMode: 'disabled',
            openAnalyzer: false,
        },
    },
    transpileDependencies: ['vuex-persist', 'jsencrypt'],
};
