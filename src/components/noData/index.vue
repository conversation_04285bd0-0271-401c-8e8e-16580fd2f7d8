<template>
    <div class="no-data">
        <div class="no-data-content">
            <i class="el-icon-ssq-meiyoujieguo1"></i>
            <p>{{ text || $t('contractCompare.noData') }}</p>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        text: {
            type: String,
            default: '',
        },
    },
};
</script>

<style lang="scss">
$--border-color-base: #CCCCCC;
.no-data {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    &-content {
        width: 80px;
        height: 112px;
        font-size: 14px;
        color: $--border-color-base;
        text-align: center;
        i {
            font-size: 70px;
            margin-bottom: 20px;
        }
    }
}
</style>
