<template>
    <div class="expand-box">
        <div class="expand-box__header">
            <div v-show="!isOpen">{{ $t('agent.revision') }}</div>
            <div v-show="isOpen" class="expand-box__tab">
                <span :class="{active: tab === title}" v-for="tab in tabList" :key="tab" @click="handleTabChange(tab)">{{ tab }}</span>
                <span class="line"></span>
            </div>
            <i :class="`el-icon-ssq-${isOpen ? 'shouqi1' : 'expand'}`" @click="toggle"></i>
        </div>
        <el-collapse-transition>
            <div v-show="isOpen" class="expand-box__content" ref="content">
                <VueMarkdown
                    :source="content"
                ></VueMarkdown>
            </div>
        </el-collapse-transition>
    </div>
</template>

<script>
import VueMarkdown from 'vue-markdown';
const jsDiff = require('diff');
export default {
    name: 'ExpandableBox',
    components: {
        VueMarkdown,
    },
    props: {
        open: {
            type: Boolean,
            default: true,
        },
        buffer: {
            type: Object,
            default: () => {},
        },
        currentTab: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            title: this.$t('agent.original'),
            isOpen: this.open, // 默认展开
            tabList: [
                this.$t('agent.original'),
                this.$t('agent.revision'),
                this.$t('agent.diff'),
            ],
        };
    },
    computed: {
        content() {
            if (this.title === this.$t('agent.diff')) {
                return this.handleDiff();
            }
            if (this.title === this.$t('agent.original')) {
                return this.buffer.originalContent;
            } else {
                return this.buffer.proposalContent;
            }
        },
    },
    watch: {
        currentTab(val) {
            if (val) {
                this.title = val;
            }
        },
    },
    methods: {
        toggle() {
            this.isOpen = !this.isOpen; // 切换展开/收起状态
        },
        handleTabChange(tab) {
            this.title = tab;
        },
        handleDiff() {
            let str = '';
            const { originalContent, proposalContent } = this.buffer;
            const diffArr = jsDiff.diffChars(originalContent, proposalContent);
            diffArr.forEach(el => {
                if (el.added) {
                    str += `<span style="color: #127fd2;">${el.value}</span>`;
                } else if (el.removed) {
                    str += `<del style="color: #FF5500;">${el.value}</del>`;
                } else {
                    str += el.value;
                }
            });
            return str;
        },
    },
};
</script>

<style lang="scss">
.expand-box {
    border: 1px solid #ccc;
    border-radius: 5px;
    margin: 15px 0;
    padding: 10px;
    background: #fff;
    &__header {
        display: flex;
        justify-content: space-between;
        line-height: 25px;
        i {
            font-size: 18px;
            width: 25px;
            text-align: center;
            border-radius: 5px;
            line-height: 25px;
            cursor: pointer;
            &:hover{
                background: #f8f8f8;
            }
        }
    }
    &__content {
        padding-top: 10px;
        border-top: 1px solid #ddd;
    }
    &__tab{
        padding-bottom: 5px;
        display: flex;
        cursor: pointer;
        span{
            font-size: 12px;
            padding: 0 10px;
            border-radius: 15px;
            &.active{
                background: #e9f3fa;
                color: $theme-color;
            }
        }
    }
}
</style>
