<template>
    <div>
        <div class="contract-comparison__header">
            <div class="head-left">
                <div class="icon" @click="goHistory">
                    <i class="el-icon-arrow-left"></i>
                </div>
                <div class="title">{{ productConfig.title }}</div>
            </div>
            <!-- <div class="head-right">
                <el-button type="primary" :loading="backLoading" @click="backToSign" v-if="couldBackToSign">{{ backBtnText }}</el-button>
                <el-tooltip :content="$t('contractCompare.myPackage')" placement="bottom">
                    <div class="btn" @click="openPackageDialog"><i class="el-icon-ssq-Hubblewodetaocan"></i></div>
                </el-tooltip>
                <el-tooltip :content="$t('contractCompare.packagePurchase')" placement="bottom">
                    <div class="btn" @click="openChargeDialog"><i class="el-icon-ssq-Hubblechongzhi"></i></div>
                </el-tooltip>
            </div> -->
        </div>
        <MyPackage :show="showPackageDialog" :toolType="config.toolType" :productConfig="productConfig" @handleClose="showPackageDialog = false" />
        <div class="compare-loading recognize-loading" v-show="showBackTip" style="top: 70px">
            <div class="compare-loading_content">
                <img src="~@/assets/images/contractHubble/hubble-loading.gif" alt="">
                <p v-show="showBackTip">正在{{ backBtnText }}页</p>
            </div>
        </div>
    </div>
</template>

<script>
import MyPackage from '../myPackage/index.vue';
import { getMyRecieversByType } from '@/api/sign';

export default {
    components: {
        MyPackage,
    },
    props: {
        config: {
            type: Object,
            default: () => ({
                productType: 15, // 比对：15，翻译：16，抽取：21
                toolType: '比对', // 比对/翻译
            }),
        },
    },
    data() {
        return {
            showChargeDialog: false,
            showPackageDialog: false,
            showBackTip: false,
            backLoading: false,
            productMap: {
                15: {
                    title: this.$t('contractCompare.title'),
                    per: this.$t('hubblePackage.pages'),
                    unitPer: this.$t('hubblePackage.pages'),
                },
                16: {
                    title: this.$t('contractCompare.translate'),
                    per: '字',
                    unitPer: this.$t('hubblePackage.words'),
                },
                17: {
                    title: this.$t('contractCompare.review'),
                    per: this.$t('hubblePackage.pages'),
                    unitPer: this.$t('hubblePackage.pages'),
                },
                21: {
                    title: this.$t('contractCompare.extract'),
                    per: this.$t('hubblePackage.pages'),
                    unitPer: this.$t('hubblePackage.pages'),
                },
                22: {
                    title: this.$t('contractCompare.riskJudgement'),
                    per: this.$t('hubblePackage.copy'),
                    unitPer: this.$t('hubblePackage.copy'),
                },
                23: {
                    title: this.$t('keyInfoExtract.operate'),
                    per: this.$t('hubblePackage.copy'),
                    unitPer: this.$t('hubblePackage.copy'),
                },
                25: {
                    title: this.$t('judgeRisk.deepInference'),
                    per: this.$t('hubblePackage.copy'),
                    unitPer: this.$t('hubblePackage.copy'),
                },
                27: {
                    title: this.$t('judgeRisk.aiInterpret'),
                    per: this.$t('hubblePackage.copy'),
                    unitPer: this.$t('hubblePackage.copy'),
                },
            },
        };
    },
    computed: {
        productConfig() {
            const type = Object.prototype.toString.call(this.config.productType);
            if (type === '[object Number]') {
                return this.productMap[this.config.productType];
            } else if (type === '[object Array]') {
                return this.productMap[this.config.productType[0]];
            }
            return this.productMap[this.config.productType];
        },
        chargeConfig() {
            const type = Object.prototype.toString.call(this.config.productType);
            const productConfig = {};
            if (type === '[object Array]') {
                this.config.productType.forEach(ele => {
                    productConfig[ele] = JSON.parse(JSON.stringify(this.productMap[ele]));
                    if (ele === 22) {
                        productConfig[22].title = this.$t('judgeRisk.title');
                    }
                });
                return productConfig;
            } else {
                productConfig[this.config.productType] = JSON.parse(JSON.stringify(this.productMap[this.config.productType]));
                if (this.config.productType === 22) {
                    productConfig[22].title = this.$t('judgeRisk.title');
                }
                return productConfig;
            }
        },
        couldBackToSign() {
            if (sessionStorage.getItem('signingPagePath') !== null) {
                return true;
            } else {
                return false;
            }
        },
        backBtnText() {
            const text = sessionStorage.getItem('signingPagePath') ? sessionStorage.getItem('signingPagePath').includes('type=approval') ? '返回审批' : '返回签署' : '';
            return text;
        },
    },
    methods: {
        openChargeDialog() {
            this.showChargeDialog = true;
        },
        openPackageDialog() {
            this.showPackageDialog = true;
        },
        goHistory() {
            if (history.length === 1) {
                this.$router.push('/');
            } else {
                history.back();
            }
        },
        async backToSign() {
            this.showBackTip = true;
            this.backLoading = true;
            let path = sessionStorage.getItem('signingPagePath');
            if (!path.includes('entId')) {
                const { contractId } = this.$route.params;
                const type = path.includes('type=approval') ? 'approval' : 'sign';
                const { data } = await getMyRecieversByType({
                    contractId,
                    type,
                });
                const entList = data.reduce((sum, item) => {
                    const enterpriseId = item.enterpriseId;
                    // entId === '0'表示个人
                    const entName = (enterpriseId === '0' ? item.userName : item.enterpriseName) || item.userAccount;
                    // 去重
                    if (!sum.find(sumItem => sumItem.entId === enterpriseId)) {
                        sum.push({
                            entId: enterpriseId,
                            entName,
                        });
                    }
                    return sum;
                }, []);
                path = `${path}&entId=${entList[0].entId}&entName=${encodeURIComponent(entList[0].entName)}`;
                this.$router.replace(path);
            } else {
                history.back();
            }
        },
        updatePage() {
            this.showChargeDialog = false;
        },
    },
};
</script>
<style lang="scss" scoped>
    .contract-comparison__header {
        width: 100%;
        height: 63px;
        color: #FFFFFF;
        font-size: 16px;
        background: #002b45;
        display: flex;
        justify-content: space-between;
        .head-left {
            display: flex;
            div {
                height: 100%;
                line-height: 63px;
                text-align: center;
            }
            .icon {
                cursor: pointer;
                width: 62px;
                border-right: 1px solid #26475A;
            }
            .title {
                margin-left: 30px;
                color: #FFFFFF;
            }
        }
        .head-right {
            margin-right: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .btn {
                width: 30px;
                height: 30px;
                border-radius: 4px;
                margin-left: 10px;
                text-align: center;
                line-height: 30px;
                cursor: pointer;
                font-size: 30px;
                &:hover {
                    background: #004466;
                }
            }
        }
    }
    .compare-loading {
        position: fixed;
        top: calc(50% - 70px);
        left: calc(50% - 105px);
        z-index: 99999;
        &_content {
            width: 210px;
            height: 140px;
            background-color: rgba($color: #000000, $alpha: 0.6);
            font-size: 16px;
            color: #FFFFFF;
            text-align: center;
            border-radius: 4px;
            img {
                width: 60px;
                height: 60px;
                margin-top: 25px;
            }
        }
    }
    .recognize-loading {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 99999;
        display: flex;
        justify-content: center;
        align-items: center;
    }
</style>
