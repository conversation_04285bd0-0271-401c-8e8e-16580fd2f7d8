export function getExtractHistory(pageNum) {
    return Vue.$http.get('/web-api/extract-content/record/get', {
        params: {
            pageNum,
            pageSize: 10,
        },
    });
}

export function startExtract(extractTaskId, mode, preferredModel) {
    return Vue.$http.get(`/web-api/extract-content/${extractTaskId}/start-extract`, {
        params: {
            mode: mode || '',
            preferredModel: preferredModel || '',
        },
    });
}

export function initExtractTask() {
    return Vue.$http.get('/web-api/extract-content/create');
}

export function getExtractFileConfig() {
    return Vue.$http.get('/web-api/extract-content/field-config/get');
}

export function addExtractField(data) {
    return Vue.$http.post('/web-api/extract-content/field-config/append', data);
}

export function editExtractField(data) {
    return Vue.$http.post('/web-api/extract-content/field-config/modify', data);
}

export function addFieldInfo(extractTaskId, data) {
    return Vue.$http.post(`/web-api/extract-content/${extractTaskId}/submit-field`, data);
}

export function deleteFieldInfo(extractTaskId, extractSubmitFieldInfoId) {
    return Vue.$http.post(`/web-api/extract-content/${extractTaskId}/remove-submit-field/${extractSubmitFieldInfoId}`);
}

export function getExtractResult(extractTaskId) {
    return Vue.$http.get(`/web-api/extract-content/result-detail/${extractTaskId}/view`);
}

export function getHistoryExtractResult(extractTaskId) {
    return Vue.$http.get(`/web-api/extract-content/record-detail/${extractTaskId}/view`);
}

export function recognizeScan(extractTaskId) {
    return Vue.$http.get(`/web-api/extract-content/${extractTaskId}/recognize-scanned-document`);
}

export function calcBilling(extractedTaskId) {
    return Vue.$http.get(`/web-api/users/tool/calculate-billing/extract-content/${extractedTaskId}`);
}

export function deleteKeyword(extractExpectedFieldId) {
    return Vue.$http.post(`/web-api/extract-content/field-config/${extractExpectedFieldId}/remove`);
}

export function getSummary(extractedTaskId) {
    return Vue.$http.get(`/web-api/extract-content/${extractedTaskId}/view-summary`);
}

export function getConsistency(extractedTaskId, data) {
    return Vue.$http.post(`/web-api/extract-content/${extractedTaskId}/consistency`, data);
}

export function consistencyResult(extractedTaskId) {
    return Vue.$http.get(`/web-api/extract-content/${extractedTaskId}/consistency-result`);
}

export function startSummary(extractedTaskId) {
    return Vue.$http.post(`/web-api/extract-content/${extractedTaskId}/start-summary`);
}

export function startFormat(extractedTaskId, data) {
    return Vue.$http.post(`/web-api/extract-content/${extractedTaskId}/format`, data);
}

export function getFormatResult(extractedTaskId, submitFieldId) {
    return Vue.$http.get(`/web-api/extract-content/${extractedTaskId}/format-result/${submitFieldId}`);
}
