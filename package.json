{"name": "delta-fe-account-center", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "dev-ccb": "vue-cli-service serve --mode dev-ccb", "dev-ja": "vue-cli-service serve --mode development-ja", "build": "vue-cli-service build", "build-noSentry": "vue-cli-service build --mode ignore-sentry", "lint": "eslint --ext .js,.vue src --max-warnings 0", "lint-fix": "eslint --fix --ext .js,.vue src", "test": "jest --no-cache", "test:cover": "jest --no-cache --coverage"}, "dependencies": {"@auth0/auth0-spa-js": "^2.1.2", "@sentry/webpack-plugin": "^1.17.1", "axios": "^0.16.2", "clean-webpack-plugin": "^4.0.0", "core-js": "^3.6.4", "crypto-js": "^3.1.9-1", "dayjs": "^1.8.27", "element-ui": "^2.15.9", "jest-sonar-reporter": "^2.0.0", "js-confetti": "^0.11.0", "js-cookie": "^2.2.1", "js-sha1": "^0.6.0", "pdfview": "^1.10.102", "qs": "^6.9.4", "random-js": "^1.0.8", "vue": "^2.6.11", "vue-cookie": "^1.1.4", "vue-i18n": "^8.14.1", "vue-markdown": "^2.2.4", "vue-router": "^3.1.6", "vuex": "^3.1.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-plugin-eslint": "~4.3.0", "@vue/cli-plugin-router": "~4.3.0", "@vue/cli-plugin-vuex": "~4.3.0", "@vue/cli-service": "~4.3.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^8.2.6", "babel-jest": "^26.6.3", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.0", "cz-conventional-changelog": "^3.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "husky": "^4.2.3", "jest": "^26.6.3", "lint-staged": "^10.0.8", "node-sass": "^4.14.1", "regenerator-runtime": "^0.13.9", "sass": "^1.25.0", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.4", "socket.io-client": "^4.7.2", "vue-jest": "^3.0.7", "vue-template-compiler": "^2.6.11"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --ext .js,.vue --max-warnings 0"], "public-library/**/*.{js,vue}": ["eslint --ext .js,.vue --max-warnings 0"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "jestSonar": {"reportFile": "test-report.xml", "indent": 4}}