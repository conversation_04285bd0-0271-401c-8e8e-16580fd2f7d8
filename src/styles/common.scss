main{
    width: 100%;
    height: 100%;
}

// a标签
a {
    color: $--color-primary;
}

// 清除浮动
.clear {
    clear:both;
    &:after {
        clear:both; content:'.'; display:block; width: 0; height: 0; visibility:hidden;
    }
}

// 浮动
.fl {
    float: left;
}
.fr {
    float: right;
}

// cursor: default
.cur-default {
    cursor: default;
}

// cursor: pointer
.cur-pointer {
    cursor: pointer;
}

// iconfont
.icon {
   width: 1em; height: 1em;
   vertical-align: -0.15em;
   fill: currentColor;
   overflow: hidden;
}

// font-size 0 清除 inline-block 的边距
.font-size-zero{
    font-size: 0;
}

// 单行...
.etc-sigle {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

// tabel-cell
.table-cell{
    display: table-cell;
}

// inline-block
.inline-block{
    display: inline-block;
}

// border-box
.border-box{
    box-sizing: border-box;
}

// vertical-align middle
.vm{
    vertical-align: middle;
}

// text-align center
.tl{
    text-align: left;
}
.tc{
    text-align: center;
}
.tr{
    text-align: right;
}

.transparent{
    opacity: 0;
}

.default-logo{
    vertical-align: middle;
}

// input placeholder
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: $--color-text-placeholder; opacity:1;
}

::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: $--color-text-placeholder;opacity:1;
}

// chrome input黄色背景
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px white inset;
}
input[type=text]:focus,
input[type=text]:hover,
input[type=password]:focus,
textarea:focus {
    -webkit-box-shadow: 0 0 0 1000px white inset;
}

input:-ms-input-placeholder{
    color: $--color-text-placeholder;opacity:1;
}

input::-webkit-input-placeholder{
    color: $--color-text-placeholder;opacity:1;
}

// 注册模块

.register {
    input {
        width: 335px;
        height: 42px;
    }
}

// 表单错误pop提示
.validation-error {
	display: inline-block;
    z-index: 9;
    position: relative;
    box-sizing: border-box;
    height: 40px;
    line-height: 41px;
    font-size: 12px;
    color: $--color-danger;
    text-align: center;
    background-color: $--background-color-base;
    padding-left: 25px;
    padding-right: 10px;
    border: 1px solid $--color-danger;
    border-radius: 2px;
    margin-top: 1px;
    margin-left: 18px;
    word-break: keep-all;
    &:after {
        content: '';
        position: absolute;
        top: 15px;
        left: -4px;
        width: 6px;
        height: 6px;
        background-color: $--background-color-base;
        border-right: 1px solid $--color-danger;
        border-bottom: 1px solid $--color-danger;
        transform:rotate(135deg);
    }
    i {
        position: absolute;
        top: 12px;
        left: 3px;
        font-size: 15px;
    }
}
// 表单错误底部提示
.validation-error-bottom {
    color: $--color-danger;
    font-size: 12px;
    line-height: 1;
    position: absolute;
    top:100%;
    left: 0;
    .el-icon-ssq-wrong-filling{
        color: $--color-danger!important;
        font-size: 12px!important;
    }
}

// input框尾部的小图标
.el-input__icon, .cs {
    cursor: pointer;
}

.authentication {
    .el-upload {
        position: relative;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        // z-index: 10;
        .el-upload__input {
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;
            display: block;
            opacity: 0;
            -moz-opacity:0 ;
            filter: alpha(opacity=0);
        }
    }
}

.ssq-search{
    box-sizing: border-box;
    position: relative;

    .el-button--small {
        padding: 7px 9px;
    }
    .el-icon-ssq-sousuo{
        font-size: 15px;
        color: $--color-text-placeholder;
        font-weight: bold;
        &:hover {
            color: $--color-primary;
        }
    }

    .el-input__suffix i {
        padding: 8px;
        cursor: pointer;
    }

    &.el-input--small{
        .el-input__suffix i{
            padding: 6px;
        }
    }

    .el-input__inner{
        padding-right: 35px;
        font-size: 12px;
    }
}

.ssq-table {
    .ssq-table-tr-hover-block {
        display: none;
    }

    &::after {
        display: none;
    }
}

.ssq-table.el-table.el-table--enable-row-hover {
    border: 1px solid;
    border-left: none;
    border-right: none;
    border-top-color: $--border-color-lighter;
    border-bottom-color: $--border-color-lighter;
    font-size: 12px;

    td,
    th {
        padding: 0;
        height: 40px;
        min-width: 0;
        text-overflow: ellipsis;
        vertical-align: middle
    }

    &::after,
    &::before {
        display: none;
    }

    th,
    .el-table__header-wrapper thead div {
        font-size: 12px;
        font-weight: bold;
        background: $--background-color-regular;
    }

    .el-table__body-wrapper {
        overflow-x: auto;
        overflow-y: auto;

        .el-table__empty-block {
            .el-table__empty-text {
                color: $--color-text-primary;
            }
        }

        tr:hover>td {
            background-color: $--background-color-secondary;

            .ssq-table-tr-hover-block {
                display: inline-block;
            }
        }
    }

    tr,
    td,
    th.is-leaf {
        border-bottom-color: $--border-color-lighter;
    }

    // .el-table__empty-block {
    //     .el-table__empty-text {
    //         position: absolute;
    //         top: 40%;
    //     }
    // }

}

// 按钮样式
// 下一步
.btn-type-one {
    box-sizing: content-box;
    cursor: pointer;
    border: none;
    border-radius: 2px;
    border: 1px solid $--color-primary;
    background-color: $--color-primary;
    color: $--color-white;
    text-align: center;
    &:hover, &:focus, &:visited {
        background-color: $--color-primary-light-1;
        border-color: $--color-primary-light-1;
        color: $--color-white;
    }
    &.disabled {
        background-color: $background-color-dark;
        border-color: $--border-color-lighter;
        color: $--color-text-secondary;
        font-weight: bold;
    }
}
// 取消
.btn-type-two {
    box-sizing: content-box;
    cursor: pointer;
    border: 1px solid $--border-color-base;
    border-radius: 2px;
    background-color: $--background-color-regular;
    color: $--color-text-regular;
    text-align: center;
    &:hover {
        border: 1px solid $--border-color-base;
        color: $--color-text-regular;
        background-color: $--color-white;
    }

    &:focus {
        border-color: $--border-color-base;
        color: $--color-text-regular;
    }
}
// 新增分组
.btn-type-three {
    box-sizing: content-box;
    cursor: pointer;
    border-radius: 2px;
    background-color: $--color-success;
    color: $--color-white;
    text-align: center;
    &:hover {
        background-color: $--color-success-light;
    }
}
// 编辑
.btn-type-four {
    box-sizing: content-box;
    cursor: pointer;
    border-radius: 2px;
    border: 1px solid $--color-primary-light-2;
    background-color: $--color-white;
    color: $--color-primary-light-2;
    text-align: center;
    &:hover {
        background-color: $--color-primary-light-2;
        color: $--color-white;
    }
}

// 文字样式
.common-font-color {
    color: $--color-primary-light-1;
    &:hover {
      color: $--color-primary;
    }
}

// btn
.ssq-btn-confirm {
    cursor: pointer;
    border-radius: 2px;
    background-color: $--color-primary;
    color: $--color-white;
    text-align: center;
    width: 100px;
    height: 34px;
    border-radius: 2px;
    &:hover {
        background-color: $--color-primary-light-1;
        color: $--color-white;
    }
    &.el-button[disabled] {
        background: $--color-primary;
        border-color: $--color-primary;
        color: rgba(255, 255, 255, 0.5);
    }
    &.disabled {
        background-color: $background-color-dark;
        color: $--color-text-secondary;
        font-weight: bold;
    }
}
.ssq-btn-cancel {
    width: 66px;
    height: 34px;
    cursor: pointer;
    border-radius: 2px;
    background-color: $--background-color-regular;
    color: $--color-text-regular;
    text-align: center;
    border: 1px solid $--border-color-base;
    border-radius: 2px;
    &:hover {
        background-color: $--color-white;
    }
}
.ssq-blue-btn {
    box-sizing: content-box;
    width: 98px;
    height: 32px;
    line-height: 34px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 2px;
    background-color: $--color-primary;
    color: $--color-white;
    text-align: center;
    border: 1px solid $--color-primary;
    &:hover {
        background-color: $--color-primary-light-1;
        color: $--color-white;
    }
    &.disabled {
        background-color: $background-color-dark;
        color: $--color-text-secondary;
        font-weight: bold;
    }
}

// dialog UI规范
.ssq-dialog {
    .el-dialog {
        border-radius: 3px;
        box-shadow: $--color-text-regular 0px 0px 6px;
    }
    .el-dialog__header {
        padding: 25px 33px;
        border: 1px solid $--border-color-light;
        .el-dialog__title {
            font-size: 16px;
            font-weight: normal;
            color: $--color-text-primary;
        }
        .el-icon-close {
            font-size: 12px; // X大小
            font-weight: bold;
            color: $--color-text-secondary;
        }
    }
    .el-dialog__body {
        padding: 33px;
        background-color: $--color-primary-light-9;
        button {
            float: right;
        }
    }
    .el-dialog__footer {
        padding: 14px 33px;
    }
}

// 没有 title 的 dialog 样式
// 只在 src/pages/foundation/sign/field/Field.vue 中使用
.ssq-dialog__notitle {
    .el-dialog{
        width: 333px;
        border-radius: 1px;
    }
    .el-dialog__header {
        padding: 0;
    }
    .el-dialog__body {
        background-color: $--background-color-base;
    }
    .el-dialog__footer {
        padding-top: 13px;
        padding-bottom: 13px;
        .el-button+.el-button {
            margin-left: 20px;
        }
        button {
            float: left;
            width: 100px;
            height: 34px;
            border-radius: 2px;
            &:first-child {
                margin-left: 38px;
            }
        }
    }
}

/* 在 el-dialog 外部用 box-sizing-dialog 包一层，内部的 dialog 会变成 border-box，并且带有UI规范的样式
---------------------------------------------------------------- */
.ssq-home,
.doc-page,
.console-page,
.doc-detail-page,
.doc-batch-page,
.recharge-con,
.account-con,
.certification-pop-con,
.content-setAccount,
.box-sizing-dialog {
    .el-dialog__wrapper {
        * {
            box-sizing: border-box;
        }

        /* el-dialog 加上 el-dialog-bg 会带上淡蓝色的背景 */
        &.el-dialog-bg {
            .el-dialog {
                background: $--color-primary-light-9;
            }
        }

        .el-dialog {
            box-shadow: 0px 0px 10px 2px #6c6c6c;

            .el-dialog__header,
            .el-dialog__body,
            .el-dialog__footer {
                padding-left: 33px;
                padding-right: 33px;
            }

            .el-dialog__header {
                height: 65px;
                border-bottom: 1px solid $--border-color-lighter;
                border-radius: 2px 2px 0 0;
                background: $--color-white;

                .el-dialog__title {
                    color: $--color-text-primary;
                    font-size: 16px;
                    font-weight: normal;
                }

                .el-dialog__close {
                    font-size: 16px;
                    color: $--color-text-secondary;
                }
            }

            .el-dialog__body {
                padding-top: 20px;
                padding-bottom: 0;
                color: $--color-text-primary;

                .dialog_body_header {
                    padding-left: 20px;
                    padding-right: 20px;
                    margin-bottom: 25px;
                    height: 34px;
                    line-height: 34px;
                    color: $--color-text-regular;
                    background: $--color-primary-light-9;
                    font-size: 14px;
                    border-radius: 3px;

                    i {
                        margin-right: 5px;
                        color: $--color-primary;
                    }

                    &.error {
                        background: $--color-warning-lighter;

                        i {
                            color: $--color-danger;
                        }
                    }
                }

                .el-form-item__label {
                    font-size: 12px;
                }

                .content-tip {
                    margin: -20px -33px 20px;
                    padding: 0 33px;
                    line-height: 30px;
                    background: $--background-color-regular;
                    font-size: 12px;
                    color: $--color-text-secondary;
                }
            }

            .el-dialog__footer {
                padding-top: 20px;

                .dialog-footer {
                    .el-button {
                        height: 34px;
                        vertical-align: middle;
                        font-size: 12px;
                    }

                    .el-button--primary {
                        padding: 0 36px;
                        color: $--color-white;

                        // &.is-disabled{
                        // 	background: $button-disable-gary;
                        // 	border-color: $button-disable-gary;
                        // 	color: $--color-text-secondary;
                        // }
                    }

                    .el-button--default {
                        padding: 0 21px;
                        // border-color: $button-default-border;
                        // background: $button-default-bg;

                        &:hover {
                            // border-color: $button-default-border;
                            background: $--color-white;
                        }

                        span {
                            font-size: 12px;
                            color: $--color-text-primary;
                        }
                    }
                }
            }

            &.title-hide {
                .el-dialog__header {
                    display: none;
                }
            }

            &.footer-hide {
                .el-dialog__footer {
                    display: none;
                }
            }
        }
    }
}

/* 移动端 el-dialog 样式
---------------------------------------------------------------- */
.el-dialog-mobile.el-dialog__wrapper {
    @media (max-width: 768px) {
        .el-dialog {
            width: 90%;

            .el-dialog__header,
            .el-dialog__body,
            .el-dialog__footer {
                padding-left: 25px;
                padding-right: 25px;
            }

            .el-dialog__header {
                .el-dialog__title {
                    font-size: 18px;
                    color: $--color-black;
                }
            }

            .el-dialog__body {
                color: $--color-text-primary;
                font-size: 18px;
            }

            .el-dialog__footer {
                padding-top: 15px;

                .dialog-footer {

                    .el-button {
                        width: 100%;
                        height: 40px;

                        span {
                            font-size: 18px;
                        }
                    }
                }
            }
        }
    }
}

// 混合云网络不通提醒弹窗
.hybrid-network-tip-msgbox .el-dialog__wrapper{
    z-index: 99999;
    .el-dialog{
        width: 630px;

        .el-dialog__header{
            border-bottom: none;
        }

        div.el-dialog__body{
            padding-top: 20px;
            padding-bottom: 20px;
            background: $--color-white;
            text-align: left;
        }

        div.el-dialog__footer{
            padding-top: 15px;
            text-align: right;

            .dialog-footer .el-button--primary{
                width: 100px;
                text-align: center;
                padding: 0;
            }
        }
    }

    @media (max-width: 768px) {
        .el-dialog{
            width: 90%;
        }
    }
}
