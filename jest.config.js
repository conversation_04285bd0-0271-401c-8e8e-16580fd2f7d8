module.exports = {
    'moduleFileExtensions': ['*', 'js', 'vue'],
    'testResultsProcessor': 'jest-sonar-reporter',
    'moduleNameMapper': {
        '\\.(css|less)$': 'identity-obj-proxy',
        '^src(.*)$': '<rootDir>/src$1',
        '^utils(.*)$': '<rootDir>/src/utils$1',
        '^testUtils(.*)$': '<rootDir>/test/testUtils$1',
        '^const/(.*)$': '<rootDir>/src/const/$1', // const直接加$1会被当作特殊字符处理，这里规避
        '^assets(.*)$': '<rootDir>/src/assets$1',
        '^http(.*)$': '<rootDir>/src/http$1',
        '^styles(.*)$': '<rootDir>/src/styles$1',
        '^lang(.*)$': '<rootDir>/src/lang$1',
        '^img(.*)$': '<rootDir>/src/assets/images$1',
        '^plugins(.*)$': '<rootDir>/src/plugins$1',
        '^mixins(.*)$': '<rootDir>/src/mixins$1',
        '^views(.*)$': '<rootDir>/src/views/$1',
        '^components(.*)$': '<rootDir>/src/components/__mocks__/',
    },
    'transform': {
        '^.+\\.js$': 'babel-jest',
        '^.+\\.vue$': 'vue-jest',
    },
};
