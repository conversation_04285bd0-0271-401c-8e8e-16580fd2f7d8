<template>
    <el-dialog
        :visible.sync="dialogVisible"
        :modal="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        class="hubble-page__share"
    >
        {{ shareData.shareUserName }} {{ $t('share.inviteToUse') }} <br>
        {{ $t('share.fileName') }}{{ shareData.shareFileName }} <br><br>
        <p>{{ $t('share.inputShareCodeToView') }}</p><br>
        <el-input v-model="password" :placeholder="$t('share.inputShareCodeTip')"></el-input>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleSubmit">{{ $t('common.confirmWS') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: true,
            shareData: {},
            password: '',
            shareToken: this.$route.params.token,
        };
    },
    methods: {
        handleSubmit() {
            this.$http.post('/auth-center/user/hubble/topic/login', {
                token: this.shareToken,
                password: this.password,
            }).then((res) => {
                const { access_token, refresh_token } = res.data;
                this.$token.save(access_token, refresh_token);
                this.$router.push(`/hubble/shareChat/${this.shareData.topicId}`);
            });
        },
    },
    created() {
        this.$http(`/web-api/ignore/topic/share-info/${this.shareToken}`).then((res) => {
            this.shareData = res.data;
        });
    },
};
</script>

<style lang="scss">
.hubble-page__share{
    .el-dialog{
        width: 400px;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
    }
}
</style>
