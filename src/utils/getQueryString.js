export function getQueryString(name, path) {
    if (!path) {
        return null;
    }
    let urls;
    if (path.indexOf('?') !== path.lastIndexOf('?')) {
        urls = path.replace(/\?/g, '&').replace(/^.*?&/, '');
    } else {
        urls = path.replace(/^.*\?/, '');
    }
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    var r = ('?' + urls).substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}

export const encodeSearchParams = (queryObj) => {
    if (Object.prototype.toString(queryObj) !== '[object Object]') {
        throw new Error('queryObj arguments must be type of Object');
    }
    const tempAry = [];
    Object.keys(queryObj).forEach((key) => {
        let value = queryObj[key];
        if (typeof value === 'undefined') {
            value = '';
        }
        tempAry.push([key, encodeURIComponent(value)].join('='));
    });
    return tempAry.join('&');
};

export const joinPathnameAndQueryObj = (pathname, queryObj) => {
    const symbol = pathname.includes('?') ? '&' : '?';
    return `${pathname}${symbol}${encodeSearchParams(queryObj)}`;
};
