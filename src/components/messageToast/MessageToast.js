import MessageToast from './MessageToast.vue';

var plugin = {};

// 插件必须要有一个install方法
plugin.install = function(Vue) {
    //
    const ToastController = Vue.extend(MessageToast);

    // 在Vue原型实现toast的DOM挂载、以及功能实现
    // 用户可以在Vue实例（Vue单文件就是一个Vue实例）通过this.$toast来访问以下内容
    Vue.prototype.$MessageToast = (option = {}) => {
        // toast实例挂载到刚创建的div
        const instance = new ToastController().$mount(document.createElement('div'));
        // 如果用户在Vue实例中没有设置option的属性message,则直接将option的内容作为message信息进行toast内容进行显示
        instance.message = typeof option === 'string' ? option : option.message;
        instance.iconClass = option.iconClass ? option.iconClass : '';

        // 将toast的DOM挂载到body上
        document.body.appendChild(instance.$el);
    };
};

// 最后将以上内容导出，即可在其他地方进行使用
export default plugin;
