<template>
    <el-dialog
        :visible.sync="value"
        :before-close="() => $emit('input', false)"
        class="hubble__agreement-detail"
        :show-close="false"
    >
        <div class="hubble__agreement-detail-header">
            <b>{{ isEdit ? $t('agreementDetail.detail') : $t('agreementDetail.add') }}</b>
            <span v-show="isEdit">{{ $t('agreementDetail.id') }}：{{ editAgreementId }}</span>
        </div>
        <div class="hubble__agreement-detail-body">
            <div class="hubble__agreement-detail__field">
                <div class="hubble__agreement-detail__field-box">
                    <div class="hubble__agreement-detail__field-item">
                        <div class="hubble__agreement-detail__field-name">
                            <div>{{ $t('agreementDetail.file') }}</div>
                            <span class="ThemeColor" v-show="editAgreementId" @click="$emit('downloadFile', editAgreementId)">{{ $t('agreementDetail.download') }}</span>
                        </div>
                        <div class="hubble__agreement-detail__field-content">
                            <el-upload
                                :action="uploadUrl"
                                :on-success="handleUploadSuccess"
                                :on-error="handleUploadFail"
                                :show-file-list="false"
                                :headers="uploadHeaders"
                            >
                                <el-button>
                                    <i class="el-icon-ssq-shangchuan1"></i>
                                    {{ $t(editAgreementId ? 'agreementDetail.replaceFile' : 'agreementDetail.uploadFile') }}
                                </el-button>
                            </el-upload>
                        </div>
                    </div>
                    <div class="hubble__agreement-detail__field-item" v-for="field in editFieldList" :key="field.termId">
                        <div v-if="!isTaskStatusField(field.fieldName)">
                            <div class="hubble__agreement-detail__field-name">
                                <div>{{ field.fieldName }}</div>
                                <span class="ThemeColor" v-show="editAgreementId && field.fieldId" @click="getOriginData(field.fieldId)">{{ $t('agreementDetail.dataSource') }}</span>
                            </div>
                            <div class="hubble__agreement-detail__field-content">
                                <el-select v-if="field.fieldType === 'BOOLEAN'" v-model="field.fieldValue">
                                    <el-option :label="$t('agreementDetail.yes')" value="true"></el-option>
                                    <el-option :label="$t('agreementDetail.no')" value="false"></el-option>
                                </el-select>
                                <el-date-picker type="date"
                                    value-format="yyyy-MM-dd"
                                    v-else-if="field.fieldType === 'DATE'"
                                    v-model="field.fieldValue"
                                    :placeholder="$t('agreementDetail.select')"
                                    @change="setDate($event, field)"
                                ></el-date-picker>
                                <el-input clearable v-else :type="field.fieldType === 'NUMBER' ? 'number' : 'text'" :placeholder="$t('agreementDetail.input')" v-model="field.fieldValue"></el-input>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hubble__agreement-detail__field-footer">
                    <el-button type="primary" :disabled="!editAgreementId" @click="saveAgreement">{{ $t('agreementDetail.save') }}</el-button>
                    <el-button type="primary" @click="() => $emit('input', false)">{{ $t('agreementDetail.cancel') }}</el-button>
                </div>
            </div>
            <div class="hubble__agreement-detail__origin">
                <template v-if="showOrigin">
                    <div class="hubble__agreement-detail__origin-head">{{ $t('agreementDetail.dataSource') }}：</div>
                    <div class="hubble__agreement-detail__origin-content">
                        <div class="hubble__agreement-detail__origin-item" v-for="origin in currentOriginData" :key="origin.referenceId">
                            <p class="hubble__agreement-detail__origin-item-text">{{ origin.referenceData }}</p>
                            <div class="hubble__agreement-detail__origin-item-operate"><span>{{ $t('agreementDetail.page', { page: origin.contentPages }) }}</span><i class="el-icon-ssq--bs-shanchu ThemeColor" @click="deleteReference(origin.referenceId)"></i></div>
                        </div>
                    </div>
                    <div class="hubble__agreement-detail__origin-footer">
                        <div v-show="!showAdd" class="ThemeColor" @click="showAdd = true">+ {{ $t('agreementDetail.addDataSource') }}</div>
                        <div v-show="showAdd" class="hubble__agreement-detail__origin-add">
                            <el-input clearable resize="none" type="textarea" v-model="newOrigin.referenceData" class="hubble__agreement-detail__origin-add-input"></el-input>
                            <div class="hubble__agreement-detail__origin-add-footer">
                                <div class="hubble__agreement-detail__origin-add-page"><span>{{ $t('agreementDetail.pageNo') }}</span><el-input v-model="newOrigin.page"></el-input><span>{{ $t('agreementDetail.pageSuffix') }}</span></div>
                                <el-button type="primary" @click="submitReference">{{ $t('agreementDetail.submit') }}</el-button>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </el-dialog>
</template>
<script>
import { mapState } from 'vuex';
export default {
    props: {
        value: {
            type: Boolean,
        },
        agreementId: {
            type: String,
            default: '',
        },
        fieldList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            isEdit: false,
            editFieldList: [],
            editAgreementId: '',
            currentFieldId: '',
            showOrigin: false,
            currentOriginData: [],
            showAdd: false,
            newOrigin: {
                referenceData: '',
                page: '',
            },
            uploadHeaders: {
                'Authorization': `bearer ${this.$cookie.get('access_token')}`,
            },
        };
    },
    computed: {
        ...mapState(['workspaceId']),
        uploadUrl() {
            return this.editAgreementId
                ? `/web-api/workspace/${this.workspaceId}/upload-agreement-file/${this.editAgreementId}`
                : `/web-api/workspace/${this.workspaceId}/upload-agreement-file`;
        },
    },
    methods: {
        isTaskStatusField(fieldName) {
            return this.fieldList.find(el => el.fieldName === fieldName).fieldSourceName === 'AGREEMENT_EXTRACT_STATUS';
        },
        submitReference() {
            const regex = /^(\d+(,\d+)*)?$/;
            if (!this.newOrigin.referenceData) {
                return this.$MessageToast(this.$t('agreementDetail.inputDataSource'));
            } else if (!regex.test(this.newOrigin.page)) {
                return this.$MessageToast(this.$t('agreementDetail.pageFormatError'));
            }
            this.$http
                .post(
                    `/web-api/workspace/${this.workspaceId}/agreements/view-agreement-fields/add-field-reference`,
                    {
                        workspaceId: this.workspaceId,
                        agreementId: this.editAgreementId,
                        fieldId: this.currentFieldId,
                        ...this.newOrigin,
                    },
                )
                .then(() => {
                    this.showAdd = false;
                    this.getOriginData(this.currentFieldId);
                });
        },
        deleteReference(referenceId) {
            this.$confirm(this.$t('agreementDetail.confirmDelete'), this.$t('agreementDetail.tips')).then(() => {
                this.$http
                    .post(
                        `/web-api/workspace/${this.workspaceId}/agreements/view-agreement-fields/remove-field-reference`,
                        {
                            workspaceId: this.workspaceId,
                            agreementId: this.editAgreementId,
                            fieldId: this.currentFieldId,
                            referenceId,
                        },
                    )
                    .then(() => {
                        this.currentOriginData = this.currentOriginData.filter(
                            (el) => el.referenceId !== referenceId,
                        );
                    });
            });
        },
        setDate(val, data) {
            data.fieldValue = val;
        },
        saveAgreement() {
            this.$http
                .post(`/web-api/workspace/${this.workspaceId}/agreements/view-agreement-fields/modify-fields`, {
                    workspaceId: this.workspaceId,
                    agreementId: this.editAgreementId,
                    agreementFieldList: this.editFieldList.map((el) => {
                        return {
                            termId: el.termId,
                            fieldValueType: el.fieldType,
                            fieldValue: el.fieldValue,
                        };
                    }),
                })
                .then(() => {
                    this.$emit('save');
                });
        },
        handleUploadSuccess(data) {
            this.$MessageToast(this.$t('agreementDetail.uploadSuccess'));
            this.editAgreementId = data.agreementId;
            this.getFieldData();
        },
        handleUploadFail(err) {
            try {
                const index = err.message.indexOf('{');
                const errData = JSON.parse(err.message.slice(index));
                errData.message && this.$MessageToast.error(errData.message);
            } catch (error) {
                this.$MessageToast.error(err.message);
            }
        },
        getOriginData(fieldId) {
            this.currentFieldId = fieldId;
            this.$http(
                `/web-api/workspace/${this.workspaceId}/agreements/view-agreement-fields/${this.editAgreementId}/reference/${fieldId}`,
            ).then((res) => {
                this.showOrigin = true;
                this.currentOriginData = res.data.agreementFieldsReferenceList;
            });
        },
        getFieldData() {
            return this.$http(
                `/web-api/workspace/${this.workspaceId}/agreements/view-agreement-fields/${this.editAgreementId}`,
            ).then((res) => {
                this.editFieldList = res.data.viewFieldItemList;
            });
        },
        init() {
            this.editAgreementId = this.agreementId;
            this.isEdit = !!this.agreementId;
            if (this.isEdit) {
                this.getFieldData();
            } else {
                this.editFieldList = this.fieldList.map((el) => {
                    return {
                        ...el,
                        fieldValue: '',
                    };
                });
            }
        },
    },
    created() {
        this.init();
    },
};
</script>

<style lang="scss">
.hubble__agreement-detail{
    .el-dialog{
        width: 700px;
        height: 440px;
        border-radius: 8px;
        overflow: hidden;
        &__header{
            display: none;
        }
        &__body{
            height: 100%;
            padding: 0;
            display: flex;
            flex-direction: column;
        }
        .el-button{
            border-radius: 5px;
        }
    }
    &-header{
        width: 100%;
        padding: 0 20px;
        line-height: 45px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-bottom: 1px solid #f1f1f1;
        span{
            font-size: 12px;
        }
    }
    &-body{
        flex-grow: 1;
        display: flex;
    }
    &__field, &__origin{
        width: 50%;
        height: 395px;
        background: #fff;
        padding: 14px 0;
        position: relative;
    }
    &__field{
        background: #fff;
        &-item{
            margin: 0 20px 12px;
        }
        &-box{
            height: 100%;
            padding-bottom: 70px;
            overflow: auto;
        }
        &-name{
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        &-content{
            height: 40px;
            // border: 1px solid #ccc;
            .el-input{
                width: 310px;
                input{
                    border-radius: 5px;
                }
            }
            .el-button{
                padding: 0;
                width: 310px;
                line-height: 40px;
                text-align: center;
                color: $theme-color;
                background: #F1F9FF;
                border: 1px dashed #9CCFFF;
                border-radius: 5px;
            }
        }
        &-footer{
            width: 100%;
            background: #fff;
            position: absolute;
            bottom: 0;
            left: 0;
            padding: 15px 20px;
            border-top: 1px solid #f1f1f1;
            .el-button{
                padding: 7px 25px;
            }
        }
    }
    &__origin{
        background: #f8f8f8;
        padding: 15px 0;
        display: flex;
        flex-direction: column;
        &-head{
            font-size: 12px;
            line-height: 18px;
            padding: 0 20px;
        }
        &-content{
            flex-grow: 1;
            overflow: auto;
        }
        &-item{
            font-size: 12px;
            margin: 5px 20px;
            background: #fff;
            border-radius: 5px;
            padding: 10px 12px;
            &-text{
                white-space: pre-wrap;
                margin-bottom: 5px;
            }
            &-operate{
                display: flex;
                justify-content: space-between;
                color: #979797;
            }
        }
        &-footer{
            padding: 10px 20px 0;
        }
        &-add{
            &-input{
                background: transparent;
                margin-bottom: 10px;
                textarea{
                    border-radius: 5px;
                    border: 1px dashed #ccc;
                }
            }
            &-footer{
                display: flex;
                justify-content: space-between;
            }
            &-page{
                display: flex;
                line-height: 30px;
                .el-input{
                    width: 40px;
                    margin: 0 5px;
                    input{
                        height: 30px;
                        border-radius: 5px;
                    }
                }
                .el-button{
                    padding: 7px 15px;
                }
            }
        }
    }
}
</style>
