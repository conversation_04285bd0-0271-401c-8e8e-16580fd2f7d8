<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>手机</title>
    <g id="0510-Hubble" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="登录页" transform="translate(-854, -310)" fill-rule="nonzero">
            <g id="编组-3" transform="translate(210, 160)">
                <g id="块" transform="translate(580, 0)">
                    <g id="1" transform="translate(50, 136)">
                        <g id="手机" transform="translate(14, 14)">
                            <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>
                            <path d="M12,1 C13.1045695,1 14,1.8954305 14,3 L14,13 C14,14.1045695 13.1045695,15 12,15 L4,15 C2.8954305,15 2,14.1045695 2,13 L2,3 C2,1.8954305 2.8954305,1 4,1 L12,1 Z M12,2 L4,2 C3.44771525,2 3,2.44771525 3,3 L3,13 C3,13.5522847 3.44771525,14 4,14 L12,14 C12.5522847,14 13,13.5522847 13,13 L13,3 C13,2.44771525 12.5522847,2 12,2 Z M9.25,11 C9.52614237,11 9.75,11.2238576 9.75,11.5 C9.75,11.7761424 9.52614237,12 9.25,12 L6.75,12 C6.47385763,12 6.25,11.7761424 6.25,11.5 C6.25,11.2238576 6.47385763,11 6.75,11 L9.25,11 Z" id="形状结合" fill="#CCCCCC"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>