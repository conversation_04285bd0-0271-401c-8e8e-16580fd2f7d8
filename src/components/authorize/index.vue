<template>
    <el-dialog
        class="hubble-page__authorize"
        :visible.sync="value"
        :before-close="handleClose"
        :title="$t('authorize.authorizeFile')"
    >
        <p>{{ $t('authorize.authorizeTip') }}</p>
        <span slot="footer">
            <a target="_blank" href="/哈勃产品使用须知-V1-20230530.pdf">{{ $t('authorize.authorizeAgree') }}</a>
            <el-button @click="handleClose">{{ $t('common.cancel') }}</el-button>
            <el-button type="primary" @click="handleAuthorize">{{ $t('authorize.name') }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        handleClose() {
            this.$emit('input', false);
        },
        handleAuthorize() {
            this.$http.post('/web-api/users/permissions/agree-authorization-letter').then(() => {
                this.$emit('handleAuthorize');
                this.$emit('input', false);
            });
        },
    },
};
</script>

<style lang="scss">
.hubble-page__authorize .el-dialog{
    font-size: 14px;
    width: 400px;
    .el-dialog__body {
        padding: 20px 20px;
    }
    a{
        float: left;
        line-height: 35px;
    }
}
</style>
