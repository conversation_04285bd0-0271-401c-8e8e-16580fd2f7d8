import Vue from 'vue';

/**
 * 根据操作类型及合同id获取签署人信息
 * @param contractId String 合同ID
 * @param type String 企业ID
 */
export function getMyRecieversByType({ contractId, type }) {
    return Vue.$http.get(`/contract-api/contracts/${contractId}/my-receivers`, {
        params: {
            type,
        },
    });
}

/**
 * 获取小程序链接
 */
export function fetchAppletUrl(analysisId) {
    return Vue.$http.get(`/web-api/agreement-analysis/${analysisId}/applet-url`);
}
