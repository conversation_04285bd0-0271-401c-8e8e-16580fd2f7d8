import dayjs from 'dayjs';
import i18n from 'src/lang/';
// 合同管理的状态
export default {
    name: 'hubble',
    namespaced: true,
    state: {
        preQuote: {},
        startSelectQuote: false,
        quotePage: 0,
        quoteCoordinate: {},
        currentPackage: {
            currentUserPlan: {},
        },
        showPackageDialog: false,
        showSlider: true,
        documentInfo: {
            pageSize: 1,
        },
        documentType: 'doc', // 文档类型：doc 或 wps
        allowEdit: false, // 是否允许编辑
        multiVersionFileId: '', // 多版本文件ID
        riskWpsEditor: null, // WPS编辑器实例
        wpsReady: false, // WPS是否准备就绪
    },
    mutations: {
        setQuote(state, data) {
            state.preQuote = data;
        },
        setPackage(state, data) {
            const currentUserPlan = data.currentUserPlan || {};
            const { chatBalance, documentBalance, initialChatCount, initialDocumentCount } = currentUserPlan;
            state.currentPackage = {
                ...data,
                currentUserPlan: {
                    ...currentUserPlan,
                    chatUsage: initialChatCount - chatBalance,
                    chatPercent: (initialChatCount - chatBalance) / initialChatCount * 100,
                    documentUsage: initialDocumentCount - documentBalance,
                    documentPercent: (initialDocumentCount - documentBalance) / initialDocumentCount * 100,
                    packageName: { PERSON_BASIC: i18n.t('package.personVersion'), TRIAL: i18n.t('package.tryVersion') }[currentUserPlan.planType],
                    effectiveTime: dayjs(currentUserPlan.effectiveTime).format('YYYY.MM.DD'),
                    expireTime: dayjs(currentUserPlan.expireTime).format('YYYY.MM.DD'),
                },
                planOverviewEffectiveTime: dayjs(data.planOverviewEffectiveTime).format('YYYY.MM.DD'),
                planOverviewExpireTime: dayjs(data.planOverviewExpireTime).format('YYYY.MM.DD'),
            };
        },
        togglePackageDialog(state, bool) {
            state.showPackageDialog = bool;
        },
        toggleSlider(state, bool) {
            state.showSlider = bool;
        },
        setDocumentInfo(state, data) {
            state.documentInfo = data;
        },
    },
    getters: {
        documentId(state) {
            return state.documentInfo.documentId;
        },
    },
};
