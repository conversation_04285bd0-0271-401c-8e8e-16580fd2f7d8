import Vue from 'vue';

import { joinPathnameAndQueryObj } from '../utils/getQueryString';
import i18n from 'lang';

// 切换导航栏偶尔报错'Loading chunk *** failed'
export function routerOnErrorFun(error) {
    const pattern = /Loading chunk.+.failed/g;
    const isChunkLoadFailed = error.message.match(pattern);
    if (isChunkLoadFailed) {
        Vue.$MessageToast.info(i18n.t('tips.pageExpired')); // 页面已过期，请刷新重试
    }
}

/**
 * 覆写VueRouter push repalce，支持传入参数location为完整的url地址【使用原生location跳转】
 * 原因：新版签署迁移到新项目，参数跳转方式支持
 * 参考VueRouter push repalce 写法 https://github.com/vuejs/vue-router/blob/dev/src/index.js
 * 有需要的可以自行引入
 */
export function routerPushFun(location, onComplete, onAbort) {
    const isStr = typeof location === 'string';
    const t = isStr ? location : location.path;
    if (/^http/.test(t)) {
        return window.location.href = isStr ? location : joinPathnameAndQueryObj(location.path, location.query);
    }
    this.history.push(location, onComplete, onAbort);
}
export function routerReplaceFun(location, onComplete, onAbort) {
    const isStr = typeof location === 'string';
    const t = isStr ? location : location.path;
    if (/^http/.test(t)) {
        return window.location.replace(isStr ? location : joinPathnameAndQueryObj(location.path, location.query));
    }
    this.history.replace(location, onComplete, onAbort);
}

export async function publicBeforeEachHooks(to, from, next) {
    if (!to.matched.length) {
        return next({
            path: '/notFound',
        });
    }
    next();
}
