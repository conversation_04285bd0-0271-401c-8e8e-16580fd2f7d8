<template>
    <el-upload
        :class="loading ? 'loading' : ''"
        :drag="drag"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="checkFile"
        :show-file-list="false"
        action="/web-api/topic/init-document"
        :headers="uploadHeaders"
        v-loading="loading && needLoading"
        :disabled="loading"
    >
        <slot></slot>
    </el-upload>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
export default {
    props: {
        drag: {
            type: Boolean,
            default: false,
        },
        needLoading: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            loading: false,
            uploadHeaders: {
                Authorization: `bearer ${this.$cookie.get('access_token')}`,
            },
        };
    },
    computed: {
        ...mapState('hubble', ['currentPackage']),
    },
    methods: {
        ...mapMutations('hubble', ['togglePackageDialog']),
        handleSuccess(res) {
            this.loading = false;
            this.$emit('onUploadSuccess', res);
        },
        handleError(err) {
            this.loading = false;
            if (err.message.includes('00032') || err.message.includes('00033')) {
                this.$MessageToast.error(this.$t('tips.packageNotEnough'));
                this.togglePackageDialog(true);
                return;
            }
            this.$MessageToast.error(this.$t('tips.uploadFailed'));
        },
        checkFile(file) {
            if (!this.currentPackage.planType) {
                this.togglePackageDialog(true);
                this.$MessageToast.error(this.$t('tips.pleaseOpenVersion'));
                return false;
            }
            const isPDF = file.type === 'application/pdf';
            // const isWord = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            if (!isPDF) {
                this.$MessageToast.error(this.$t('tips.pdfSupportOnly'));
                return false;
            }
            this.loading = true;
            return true;
        },
    },
};
</script>
<style lang="scss">
.hubble-page__upload{
    width: 100%;
    height: 100%;
    padding: 40px;
    box-sizing: border-box;
    background: #f8f8f8;
    &-wrapper{
        .el-upload-dragger{
            width: calc(100vw - 80px);
            height: calc(100vh - 150px);
            border: 3px dashed rgba(221,221,221,1);
            border-radius: 8px;
            position: relative;
            background: transparent;
            .hubble-page__upload-drag-text{
                color: #0C8AEE;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                display: none;
            }
            &:hover, &.is-dragover{
                border-color: #0C8AEE;
            }
            &.is-dragover{
                .hubble-page__upload-operate{
                    display: none;
                }
                .hubble-page__upload-drag-text{
                    display: block;
                }
            }
        }
    }
    &-operate{
        color: #999;
        width: 150px;
        text-align: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        i{
            font-size: 56px;
            margin-bottom: 20px;
        }
        p{
            font-size: 16px;
            margin-bottom: 14px;
        }
        span.accept{
            color: #ddd;
            font-size: 12px;
        }
        .el-button{
            margin-top: 30px;
        }
    }
    .el-loading-mask{
        z-index: 1000;
    }
}
</style>
